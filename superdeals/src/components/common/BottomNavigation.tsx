import { Home, Tag, User, Search, Heart } from 'lucide-react';
import svgPathsOffers from '../../imports/svg-5k9z8vl408';
import type { Screen } from '../../types';

interface BottomNavigationProps {
  activeTab: string;
  onNavigate: (screen: Screen) => void;
}

export function BottomNavigation({ activeTab, onNavigate }: BottomNavigationProps) {
  const navigationItems = [
    { id: 'home', label: 'হোম', screen: 'home' as Screen, iconPath: svgPathsOffers.p29dd2c00, viewBox: '0 0 18 19' },
    { id: 'offers', label: 'অফারসমূহ', screen: 'offers' as Screen, iconPath: svgPathsOffers.p19077c00, viewBox: '0 0 18 18' },
    { id: 'search', label: 'খুঁজুন', screen: 'search' as Screen, icon: Search },
    { id: 'favorites', label: 'পছন্দের', screen: 'favorites' as Screen, icon: Heart },
    { id: 'profile', label: 'প্রোফাইল', screen: 'profile' as Screen, iconPath: svgPathsOffers.p3d54cd00, viewBox: '0 0 20 20' }
  ];

  return (
    <div className="bg-[#261c1c] border-t border-[#382929]">
      <div className="flex justify-around px-2 pt-[9px] pb-3">
        {navigationItems.map((item) => (
          <button
            key={item.id}
            onClick={() => onNavigate(item.screen)}
            className="flex flex-col items-center gap-1 flex-1 min-w-0"
          >
            <div className="h-8 w-8 flex items-center justify-center">
              {item.icon ? (
                <item.icon className={`h-5 w-5 ${
                  activeTab === item.id ? 'text-white' : 'text-[#b89e9e]'
                }`} />
              ) : (
                <svg viewBox={item.viewBox} className="w-5 h-5">
                  <path
                    d={item.iconPath}
                    fill={activeTab === item.id ? 'white' : '#b89e9e'}
                    fillRule="evenodd"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </div>
            <span className={`text-[10px] leading-[15px] font-medium truncate ${
              activeTab === item.id ? 'text-white' : 'text-[#b89e9e]'
            }`}>
              {item.label}
            </span>
          </button>
        ))}
      </div>
      <div className="bg-[#261c1c] h-5" />
    </div>
  );
}