import { <PERSON>Left, Search, Heart } from 'lucide-react';
import { Button } from '../ui/button';
import svgPaths from '../../imports/svg-l3n9c2ec8v';
import svgPathsOffers from '../../imports/svg-5k9z8vl408';
import type { Screen } from '../../types';

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  onBack?: () => void;
  showSearch?: boolean;
  onSearch?: () => void;
  showFavorites?: boolean;
  onFavorites?: () => void;
  currentScreen?: Screen;
}

export function Header({ 
  title, 
  showBackButton = false, 
  onBack,
  showSearch = false,
  onSearch,
  showFavorites = false,
  onFavorites,
  currentScreen
}: HeaderProps) {
  return (
    <div className="bg-[#171212] px-4 pt-4 pb-2">
      <div className="flex items-center justify-between">
        {showBackButton ? (
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="text-white p-0 h-12 w-12"
          >
            <svg viewBox="0 0 24 24" className="w-6 h-6">
              <path
                d={svgPathsOffers.p242cebf0}
                fill="white"
                fillRule="evenodd"
                clipRule="evenodd"
              />
            </svg>
          </Button>
        ) : (
          <div className="w-12" />
        )}
        
        <div className="flex-1 flex justify-center">
          <h1 className="text-white text-[18px] font-bold leading-[23px]">
            {title}
          </h1>
        </div>
        
        <div className="flex items-center gap-2">
          {showSearch && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onSearch}
              className="text-white p-0 h-8 w-8"
            >
              <Search className="h-5 w-5" />
            </Button>
          )}
          
          {showFavorites && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onFavorites}
              className="text-white p-0 h-8 w-8"
            >
              <Heart className="h-5 w-5" />
            </Button>
          )}
          
          <div className="w-6 h-6">
            <svg viewBox="0 0 21 21" className="w-full h-full">
              <path
                d={svgPaths.p13c25380}
                fill="white"
                fillRule="evenodd"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
}