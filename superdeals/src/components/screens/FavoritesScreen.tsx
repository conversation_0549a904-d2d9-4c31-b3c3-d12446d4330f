'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, Search, User, Grid3X3, Home, ArrowLeft } from 'lucide-react';
import { homeOffers } from '@/data/homeOffers';
import type { Screen, Offer } from '@/types';

interface FavoritesScreenProps {
  onNavigate: (screen: Screen) => void;
  onOfferSelect: (offer: Offer) => void;
  activeTab: string;
}

export function FavoritesScreen({ onNavigate, onOfferSelect, activeTab }: FavoritesScreenProps) {
  const [favorites, setFavorites] = useState<string[]>(['1', '3']); // Default favorites

  const favoriteOffers = homeOffers.filter(offer => favorites.includes(offer.id));

  const toggleFavorite = (offerId: string) => {
    setFavorites(prev => 
      prev.includes(offerId) 
        ? prev.filter(id => id !== offerId)
        : [...prev, offerId]
    );
  };

  const handleOfferClick = (offer: Offer) => {
    onOfferSelect(offer);
    onNavigate('offer-detail');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-3 mb-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onNavigate('home')}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-semibold text-gray-800">Favorites</h1>
        </div>
        <p className="text-sm text-gray-600 ml-12">
          {favoriteOffers.length} saved offers
        </p>
      </div>

      <div className="p-4">
        {favoriteOffers.length > 0 ? (
          <div className="space-y-3">
            {favoriteOffers.map((offer) => (
              <Card 
                key={offer.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleOfferClick(offer)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge 
                          variant="secondary" 
                          className="text-xs"
                          style={{
                            backgroundColor: offer.carrier === 'Grameenphone' ? '#00A651' :
                                            offer.carrier === 'Airtel' ? '#E31E24' :
                                            offer.carrier === 'Robi' ? '#FF6B00' :
                                            offer.carrier === 'Banglalink' ? '#0066CC' : '#6B7280',
                            color: 'white'
                          }}
                        >
                          {offer.carrier}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {offer.type}
                        </Badge>
                      </div>
                      <h3 className="font-semibold text-gray-800 mb-1">{offer.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">{offer.description}</p>
                      <div className="flex items-center gap-4 text-sm">
                        <span className="text-green-600 font-semibold">{offer.data}</span>
                        <span className="text-gray-500">{offer.validity}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">{offer.myPrice}</div>
                      <div className="text-xs text-gray-500 line-through">{offer.companyPrice}</div>
                      <div className="text-xs text-red-500">Save {offer.savings}</div>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="mt-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleFavorite(offer.id);
                        }}
                      >
                        <Heart 
                          className={`h-4 w-4 ${
                            favorites.includes(offer.id) 
                              ? 'text-red-500 fill-current' 
                              : 'text-gray-400'
                          }`} 
                        />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="text-gray-400 mb-4">
              <Heart className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No favorites yet</h3>
            <p className="text-gray-500 mb-6">
              Start adding offers to your favorites to see them here
            </p>
            <Button 
              onClick={() => onNavigate('offers')}
              className="bg-green-600 hover:bg-green-700"
            >
              Browse Offers
            </Button>
          </div>
        )}

        {/* Quick Actions */}
        {favoriteOffers.length > 0 && (
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="h-16 flex-col gap-2"
                onClick={() => onNavigate('offers')}
              >
                <Grid3X3 className="h-6 w-6" />
                <span className="text-sm">Browse More</span>
              </Button>
              <Button
                variant="outline"
                className="h-16 flex-col gap-2"
                onClick={() => onNavigate('search')}
              >
                <Search className="h-6 w-6" />
                <span className="text-sm">Search Offers</span>
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="max-w-sm mx-auto">
          <div className="grid grid-cols-5 py-2">
            <Button
              variant="ghost"
              className={`flex-col gap-1 h-16 ${activeTab === 'home' ? 'text-green-600' : 'text-gray-400'}`}
              onClick={() => onNavigate('home')}
            >
              <Home className="h-5 w-5" />
              <span className="text-xs">Home</span>
            </Button>
            <Button
              variant="ghost"
              className={`flex-col gap-1 h-16 ${activeTab === 'offers' ? 'text-green-600' : 'text-gray-400'}`}
              onClick={() => onNavigate('offers')}
            >
              <Grid3X3 className="h-5 w-5" />
              <span className="text-xs">Offers</span>
            </Button>
            <Button
              variant="ghost"
              className={`flex-col gap-1 h-16 ${activeTab === 'search' ? 'text-green-600' : 'text-gray-400'}`}
              onClick={() => onNavigate('search')}
            >
              <Search className="h-5 w-5" />
              <span className="text-xs">Search</span>
            </Button>
            <Button
              variant="ghost"
              className={`flex-col gap-1 h-16 ${activeTab === 'favorites' ? 'text-green-600' : 'text-gray-400'}`}
              onClick={() => onNavigate('favorites')}
            >
              <Heart className="h-5 w-5" />
              <span className="text-xs">Favorites</span>
            </Button>
            <Button
              variant="ghost"
              className={`flex-col gap-1 h-16 ${activeTab === 'profile' ? 'text-green-600' : 'text-gray-400'}`}
              onClick={() => onNavigate('profile')}
            >
              <User className="h-5 w-5" />
              <span className="text-xs">Profile</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-20"></div>
    </div>
  );
}
