import { Button } from '../ui/button';
import { Header } from '../common/Header';
import { BottomNavigation } from '../common/BottomNavigation';
import offerDetailImg from "figma:asset/9e863891c02915bfd9965a4802b4e0c724db11b9.png";
import type { Screen, Offer } from '../../types';

interface OfferDetailScreenProps {
  offer: Offer;
  onNavigate: (screen: Screen) => void;
  activeTab: string;
}

interface DetailRowProps {
  label1: string;
  value1: string;
  label2: string;
  value2: string;
}

function DetailRow({ label1, value1, label2, value2 }: DetailRowProps) {
  return (
    <div className="flex gap-6">
      <div className="w-[72px]">
        <div className="border-t border-[#e5e8eb] pt-[21px] pb-5">
          <p className="text-[#b89e9e] text-[14px] leading-[21px] mb-2">{label1}</p>
          <p className="text-white text-[14px] leading-[21px]">{value1}</p>
        </div>
      </div>
      <div className="flex-1">
        <div className="border-t border-[#e5e8eb] pt-[21px] pb-5">
          <p className="text-[#b89e9e] text-[14px] leading-[21px] mb-2">{label2}</p>
          <p className="text-white text-[14px] leading-[21px]">{value2}</p>
        </div>
      </div>
    </div>
  );
}

export function OfferDetailScreen({ offer, onNavigate, activeTab }: OfferDetailScreenProps) {
  const handleCall = () => {
    window.open(`tel:${offer.phone}`, '_self');
  };

  const handleWhatsApp = () => {
    const message = encodeURIComponent(`I'm interested in the ${offer.title} offer. Please provide more details.`);
    window.open(`https://wa.me/${offer.phone.replace(/[^0-9]/g, '')}?text=${message}`, '_blank');
  };

  const handleTelegram = () => {
    window.open(`https://t.me/${offer.phone.replace(/[^0-9]/g, '')}`, '_blank');
  };

  return (
    <div className="bg-[#171212] min-h-screen flex flex-col">
      <Header 
        title="Offer Details" 
        showBackButton={true} 
        onBack={() => onNavigate('offers')}
      />
      
      {/* Hero Image */}
      <div 
        className="h-[218px] bg-cover bg-center"
        style={{ backgroundImage: `url('${offerDetailImg}')` }}
      />

      {/* Offer Title */}
      <div className="px-4 pt-5 pb-3">
        <h2 className="text-white text-[22px] font-bold leading-[28px]">
          {offer.data} Data Offer
        </h2>
      </div>

      {/* Savings */}
      <div className="px-4 pt-1 pb-3">
        <p className="text-[#b89e9e] text-[14px] leading-[21px]">
          Save {offer.savings}
        </p>
      </div>

      {/* Details Section */}
      <div className="px-4 pt-4 pb-2">
        <h3 className="text-white text-[18px] font-bold leading-[23px]">Details</h3>
      </div>

      {/* Details Table */}
      <div className="px-4 py-4">
        <div className="space-y-6">
          <DetailRow
            label1="Data"
            value1={offer.data}
            label2="Validity"
            value2={offer.validity}
          />

          <DetailRow
            label1="Type"
            value1={offer.type}
            label2="Company Price"
            value2={offer.companyPrice}
          />

          <DetailRow
            label1="My Price"
            value1={offer.myPrice}
            label2="You Save"
            value2={offer.savings}
          />
        </div>
      </div>

      {/* Contact Section */}
      <div className="px-4 pt-4 pb-2">
        <h3 className="text-white text-[18px] font-bold leading-[23px]">Contact</h3>
      </div>

      {/* Contact Info */}
      <div className="bg-[#171212] h-14 px-4 flex items-center justify-between">
        <p className="text-white text-[16px] leading-[24px]">{offer.phone}</p>
        <Button 
          onClick={handleCall}
          variant="secondary" 
          size="sm" 
          className="bg-[#382929] text-white border-none hover:bg-[#4a3535] h-8 px-4"
        >
          <span className="text-[14px] leading-[21px] font-medium">Call</span>
        </Button>
      </div>

      {/* Action Buttons */}
      <div className="px-4 py-3 flex gap-3">
        <Button 
          onClick={handleWhatsApp}
          className="bg-[#e82933] hover:bg-[#d12730] text-white h-10 px-4 flex-1"
        >
          <span className="text-[14px] leading-[21px] font-bold">WhatsApp</span>
        </Button>
        <Button 
          onClick={handleTelegram}
          className="bg-[#e82933] hover:bg-[#d12730] text-white h-10 px-4 flex-1"
        >
          <span className="text-[14px] leading-[21px] font-bold">Telegram</span>
        </Button>
      </div>

      <div className="flex-1" />

      <BottomNavigation activeTab={activeTab} onNavigate={onNavigate} />
    </div>
  );
}