import { useState } from 'react';
import { Search } from 'lucide-react';
import { Input } from '../ui/input';
import { Header } from '../common/Header';
import { BottomNavigation } from '../common/BottomNavigation';
import { OfferCard } from '../common/OfferCard';
import { useOffers } from '../../hooks/useOffers';
import type { Screen, Offer } from '../../types';

interface SearchScreenProps {
  onNavigate: (screen: Screen) => void;
  onOfferSelect: (offer: Offer) => void;
  activeTab: string;
}

export function SearchScreen({ onNavigate, onOfferSelect, activeTab }: SearchScreenProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const { offers, toggleFavorite } = useOffers({ searchQuery });

  const handleOfferClick = (offer: Offer) => {
    onOfferSelect(offer);
    onNavigate('offer-detail');
  };

  const recentSearches = ['গ্রামীণফোন', 'বাংলালিংক', '১০জিবি', 'কম্বো'];
  const trendingSearches = ['৫০জিবি', 'মিনিট', 'ৰোবি', 'সুপার ডিল'];

  return (
    <div className="bg-[#171212] min-h-screen flex flex-col">
      <Header 
        title="Search Offers" 
        showBackButton={true} 
        onBack={() => onNavigate('home')}
      />

      {/* Search Input */}
      <div className="px-4 py-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#b89e9e] h-4 w-4" />
          <Input
            type="text"
            placeholder="অফার খুঁজুন..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-[#382929] border-none text-white placeholder-[#b89e9e] pl-10 h-12 text-[16px]"
          />
        </div>
      </div>

      {/* Search Results */}
      {searchQuery ? (
        <div className="flex-1">
          {offers.length > 0 ? (
            <div>
              <div className="px-4 py-2">
                <p className="text-[#b89e9e] text-[14px]">
                  {offers.length} টি অফার পাওয়া গেছে "{searchQuery}" এর জন্য
                </p>
              </div>
              {offers.map((offer) => (
                <OfferCard 
                  key={offer.id} 
                  offer={offer} 
                  onClick={handleOfferClick}
                  onToggleFavorite={toggleFavorite}
                  showFavoriteButton={true}
                />
              ))}
            </div>
          ) : (
            <div className="px-4 py-8 text-center">
              <p className="text-[#b89e9e] text-[16px]">কোন অফার পাওয়া যায়নি</p>
              <p className="text-[#b89e9e] text-[14px] mt-2">অন্য কিছু দিয়ে খুঁজে দেখুন</p>
            </div>
          )}
        </div>
      ) : (
        <div className="flex-1 px-4">
          {/* Recent Searches */}
          <div className="py-4">
            <h3 className="text-white text-[16px] font-bold mb-3">সাম্প্রতিক অনুসন্ধান</h3>
            <div className="flex flex-wrap gap-2">
              {recentSearches.map((search) => (
                <button
                  key={search}
                  onClick={() => setSearchQuery(search)}
                  className="bg-[#382929] text-[#b89e9e] px-3 py-2 rounded-lg text-[14px] hover:bg-[#4a3535] hover:text-white transition-colors"
                >
                  {search}
                </button>
              ))}
            </div>
          </div>

          {/* Trending Searches */}
          <div className="py-4">
            <h3 className="text-white text-[16px] font-bold mb-3">জনপ্রিয় অনুসন্ধান</h3>
            <div className="flex flex-wrap gap-2">
              {trendingSearches.map((search) => (
                <button
                  key={search}
                  onClick={() => setSearchQuery(search)}
                  className="bg-[#382929] text-[#b89e9e] px-3 py-2 rounded-lg text-[14px] hover:bg-[#4a3535] hover:text-white transition-colors"
                >
                  🔥 {search}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      <BottomNavigation activeTab={activeTab} onNavigate={onNavigate} />
    </div>
  );
}