import { useState } from 'react';
import { Badge } from '../ui/badge';
import { Header } from '../common/Header';
import { BottomNavigation } from '../common/BottomNavigation';
import { OfferCard } from '../common/OfferCard';
import { homeOffers, categories, filterChips } from '../../data/offers';
import { useOffers } from '../../hooks/useOffers';
import type { Screen, Offer } from '../../types';

interface HomeScreenProps {
  onNavigate: (screen: Screen) => void;
  onOfferSelect: (offer: Offer) => void;
  activeTab: string;
}

export function HomeScreen({ onNavigate, onOfferSelect, activeTab }: HomeScreenProps) {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const { toggleFavorite } = useOffers();

  const filteredHomeOffers = homeOffers.filter(offer => {
    if (selectedCategory === 'all') return true;
    if (selectedCategory === 'grameenphone') return offer.carrier.includes('গ্রামীণফোন');
    if (selectedCategory === 'banglalink') return offer.carrier.includes('বাংলালিংক');
    if (selectedCategory === 'robi') return offer.carrier.includes('ৰোবি');
    if (selectedCategory === 'airtel') return offer.carrier.includes('এয়ারটেল');
    return true;
  });

  const handleOfferClick = (offer: Offer) => {
    onOfferSelect(offer);
    onNavigate('offer-detail');
  };

  return (
    <div className="bg-[#171212] min-h-screen flex flex-col">
      <Header 
        title="রিয়াদের সিম অফার" 
        showSearch={true}
        onSearch={() => onNavigate('search')}
        showFavorites={true}
        onFavorites={() => onNavigate('favorites')}
      />
      
      {/* Balance */}
      <div className="px-4 pt-1 pb-3">
        <p className="text-[#b89e9e] text-[14px] leading-[21px]">৳০.১</p>
      </div>

      {/* Category Tabs */}
      <div className="border-b border-[#543d3d] px-4 pb-px">
        <div className="flex gap-8 overflow-x-auto">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`py-4 whitespace-nowrap border-b-[3px] text-[14px] leading-[21px] font-bold ${
                selectedCategory === category.id
                  ? 'text-white border-white'
                  : 'text-[#b89e9e] border-transparent'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>
      </div>

      {/* Filter Chips */}
      <div className="px-3 py-3">
        <div className="flex gap-3 overflow-x-auto">
          {filterChips.map((chip) => (
            <Badge 
              key={chip.id}
              className="bg-[#382929] text-white text-[14px] leading-[21px] font-medium border-none px-4 py-0 h-8 hover:bg-[#382929]"
            >
              {chip.label}
            </Badge>
          ))}
        </div>
      </div>

      {/* Offers List */}
      <div className="flex-1">
        {filteredHomeOffers.map((offer) => (
          <OfferCard 
            key={offer.id} 
            offer={offer} 
            onClick={handleOfferClick}
            onToggleFavorite={toggleFavorite}
            showFavoriteButton={true}
          />
        ))}
      </div>

      <BottomNavigation activeTab={activeTab} onNavigate={onNavigate} />
    </div>
  );
}