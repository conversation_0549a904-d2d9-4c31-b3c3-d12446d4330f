import { useState } from 'react';
import { Badge } from '../ui/badge';
import { Slider } from '../ui/slider';
import { Header } from '../common/Header';
import { BottomNavigation } from '../common/BottomNavigation';
import { OfferCard } from '../common/OfferCard';
import { offerCategories, validityOptions } from '../../data/offers';
import { useOffers } from '../../hooks/useOffers';
import type { Screen, Offer } from '../../types';

interface OffersScreenProps {
  onNavigate: (screen: Screen) => void;
  onOfferSelect: (offer: Offer) => void;
  activeTab: string;
}

export function OffersScreen({ onNavigate, onOfferSelect, activeTab }: OffersScreenProps) {
  const [selectedOfferCategory, setSelectedOfferCategory] = useState('all');
  const [selectedValidity, setSelectedValidity] = useState('');
  const [priceRange, setPriceRange] = useState([800]);

  const { offers, toggleFavorite, filteredCount, totalOffers } = useOffers({
    selectedCategory: selectedOfferCategory,
    selectedValidity,
    priceRange
  });

  const handleOfferClick = (offer: Offer) => {
    onOfferSelect(offer);
    onNavigate('offer-detail');
  };

  return (
    <div className="bg-[#171212] min-h-screen flex flex-col">
      <Header 
        title="Offers" 
        showBackButton={true} 
        onBack={() => onNavigate('home')}
        showSearch={true}
        onSearch={() => onNavigate('search')}
      />
      
      {/* Balance */}
      <div className="h-[47px] px-4 pt-4 pb-2">
        <h2 className="text-white text-[18px] font-bold leading-[23px]">
          Balance: ৳ 1,250
        </h2>
      </div>

      {/* Category Filters */}
      <div className="px-3 py-3">
        <div className="flex gap-3 overflow-x-auto">
          {offerCategories.map((category) => (
            <Badge 
              key={category.id}
              onClick={() => setSelectedOfferCategory(category.id)}
              className={`text-white text-[14px] leading-[21px] font-medium border-none px-4 py-0 h-8 cursor-pointer transition-colors ${
                selectedOfferCategory === category.id 
                  ? 'bg-[#4a3535] hover:bg-[#4a3535]' 
                  : 'bg-[#382929] hover:bg-[#4a3535]'
              }`}
            >
              {category.label}
            </Badge>
          ))}
        </div>
      </div>

      {/* Validity Section */}
      <div className="px-4 pt-4 pb-2">
        <h3 className="text-white text-[18px] font-bold leading-[23px]">Validity</h3>
      </div>
      
      <div className="px-3 py-3">
        <div className="flex flex-wrap gap-3">
          {validityOptions.map((validity) => (
            <Badge 
              key={validity.id}
              onClick={() => setSelectedValidity(selectedValidity === validity.id ? '' : validity.id)}
              className={`text-white text-[14px] leading-[21px] font-medium border-none px-4 py-0 h-8 cursor-pointer transition-colors ${
                selectedValidity === validity.id 
                  ? 'bg-[#4a3535] hover:bg-[#4a3535]' 
                  : 'bg-[#382929] hover:bg-[#4a3535]'
              }`}
            >
              {validity.label}
            </Badge>
          ))}
        </div>
      </div>

      {/* Price Range Section */}
      <div className="h-[47px] px-4 pt-4 pb-2">
        <h3 className="text-white text-[18px] font-bold leading-[23px]">Price Range</h3>
      </div>
      
      <div className="px-4 py-4">
        <div className="space-y-4">
          <p className="text-white text-[16px] leading-[24px]">
            Up to ৳{priceRange[0]} ({filteredCount} of {totalOffers} offers)
          </p>
          <Slider
            value={priceRange}
            onValueChange={setPriceRange}
            max={1000}
            min={50}
            step={50}
            className="w-full"
          />
        </div>
      </div>

      {/* Offers List */}
      <div className="flex-1">
        {offers.length > 0 ? (
          offers.map((offer) => (
            <OfferCard 
              key={offer.id} 
              offer={offer} 
              onClick={handleOfferClick}
              onToggleFavorite={toggleFavorite}
              showFavoriteButton={true}
            />
          ))
        ) : (
          <div className="px-4 py-8 text-center">
            <p className="text-[#b89e9e] text-[16px]">কোন অফার পাওয়া যায়নি</p>
            <p className="text-[#b89e9e] text-[14px] mt-2">ফিল্টার পরিবর্তন করে আবার চেষ্টা করুন</p>
          </div>
        )}
      </div>

      <BottomNavigation activeTab={activeTab} onNavigate={onNavigate} />
    </div>
  );
}