import { useState } from 'react';
import { User, Phone, Calendar, Settings, Bell, HelpCircle, LogOut, Edit } from 'lucide-react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { Header } from '../common/Header';
import { BottomNavigation } from '../common/BottomNavigation';
import { mockUser } from '../../data/offers';
import type { Screen } from '../../types';

interface ProfileScreenProps {
  onNavigate: (screen: Screen) => void;
  activeTab: string;
}

export function ProfileScreen({ onNavigate, activeTab }: ProfileScreenProps) {
  const [notifications, setNotifications] = useState(true);
  const [autoRecharge, setAutoRecharge] = useState(false);

  const menuItems = [
    { icon: Bell, label: 'Notifications', action: 'toggle', value: notifications, onChange: setNotifications },
    { icon: Settings, label: 'Auto Recharge', action: 'toggle', value: autoRecharge, onChange: setAutoRecharge },
    { icon: HelpCircle, label: 'Help & Support', action: 'navigate' },
    { icon: LogOut, label: 'Log Out', action: 'logout', color: 'text-red-500' }
  ];

  return (
    <div className="bg-[#171212] min-h-screen flex flex-col">
      <Header 
        title="Profile" 
        showBackButton={true} 
        onBack={() => onNavigate('home')}
      />

      {/* Profile Info */}
      <div className="px-4 py-6">
        <Card className="bg-[#261c1c] border-[#382929]">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-[#382929] rounded-full flex items-center justify-center">
                <User className="w-8 h-8 text-[#b89e9e]" />
              </div>
              <div className="flex-1">
                <h2 className="text-white text-[18px] font-bold">{mockUser.name}</h2>
                <div className="flex items-center space-x-2 mt-1">
                  <Phone className="w-4 h-4 text-[#b89e9e]" />
                  <p className="text-[#b89e9e] text-[14px]">{mockUser.phone}</p>
                </div>
                <div className="flex items-center space-x-2 mt-1">
                  <Calendar className="w-4 h-4 text-[#b89e9e]" />
                  <p className="text-[#b89e9e] text-[14px]">
                    Joined {new Date(mockUser.joinedDate).toLocaleDateString('en-GB')}
                  </p>
                </div>
              </div>
              <Button variant="ghost" size="icon" className="text-[#b89e9e]">
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Balance Info */}
      <div className="px-4 pb-6">
        <Card className="bg-[#261c1c] border-[#382929]">
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-[#b89e9e] text-[14px] mb-1">Current Balance</p>
              <p className="text-white text-[24px] font-bold">৳ {mockUser.balance.toLocaleString()}</p>
              <Button 
                className="bg-[#e82933] hover:bg-[#d12730] text-white mt-3 w-full"
                size="sm"
              >
                Recharge Now
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Menu Items */}
      <div className="px-4 space-y-2 flex-1">
        {menuItems.map((item, index) => (
          <Card key={index} className="bg-[#261c1c] border-[#382929]">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <item.icon className={`w-5 h-5 ${item.color || 'text-[#b89e9e]'}`} />
                  <span className={`text-[16px] ${item.color || 'text-white'}`}>
                    {item.label}
                  </span>
                </div>
                {item.action === 'toggle' && (
                  <Switch
                    checked={item.value}
                    onCheckedChange={item.onChange}
                  />
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* App Version */}
      <div className="px-4 py-4">
        <p className="text-[#b89e9e] text-[12px] text-center">
          Riyadh SIM Offers v1.0.0
        </p>
      </div>

      <BottomNavigation activeTab={activeTab} onNavigate={onNavigate} />
    </div>
  );
}