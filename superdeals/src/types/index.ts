export type Screen = 'home' | 'offers' | 'offer-detail' | 'profile' | 'search' | 'favorites' | 'settings';

export interface Offer {
  id: string;
  carrier: string;
  title: string;
  description: string;
  image: string;
  data: string;
  validity: string;
  type: 'Internet' | 'Combo' | 'Minutes' | 'Recharge';
  companyPrice: string;
  myPrice: string;
  savings: string;
  phone: string;
  price: number;
  isFavorite?: boolean;
}

export interface Provider {
  id: string;
  name_bn: string;
  name_en: string;
  color: string;
}

export interface ContactInfo {
  phone: string;
  whatsapp: string;
  telegram: string;
  business_hours: string;
}

export interface Category {
  id: string;
  label: string;
  active: boolean;
}

export interface FilterChip {
  id: string;
  label: string;
  active: boolean;
}

export interface User {
  id: string;
  name: string;
  phone: string;
  balance: number;
  avatar?: string;
  joinedDate: string;
}

export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  screen: Screen;
}