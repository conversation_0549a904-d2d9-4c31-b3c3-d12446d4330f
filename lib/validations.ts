import { z } from "zod"

export const createPostSchema = z.object({
  title: z.string().min(1, "Title is required").max(100),
  content: z.string().min(1, "Content is required"),
})

export const createUserSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email"),
})

export type CreatePostInput = z.infer<typeof createPostSchema>
export type CreateUserInput = z.infer<typeof createUserSchema>