import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'রিয়াদের সিম অফার - SIM Offers BD',
  description: 'Best SIM offers in Bangladesh at wholesale prices',
  viewport: 'width=device-width, initial-scale=1, maximum-scale=1'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="bn">
      <body className={`${inter.className} bg-gray-900 text-white min-h-screen`}>
        <div className="max-w-md mx-auto bg-gray-900 min-h-screen">
          {children}
        </div>
      </body>
    </html>
  )
}