import { SimOffersRouter } from '@/features/sim-offers'

export default function CatchAllPage({ 
  params 
}: { 
  params: { slug: string[] } 
}) {
  const [app, ...rest] = params.slug
  
  switch (app) {
    case 'sim-offers':
      return <SimOffersRouter path={rest} />
    default:
      return (
        <div className="p-6 text-center">
          <h1 className="text-xl font-bold mb-4">অ্যাপ পাওয়া যায়নি</h1>
          <p className="text-gray-400">App not found</p>
        </div>
      )
  }
}