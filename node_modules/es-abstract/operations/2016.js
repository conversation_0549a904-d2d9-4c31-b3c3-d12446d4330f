'use strict';

module.exports = {
	IsPropertyDescriptor: 'https://262.ecma-international.org/6.0/#sec-property-descriptor-specification-type', // not actually an abstract op

	abs: {
		url: 'https://262.ecma-international.org/7.0/#sec-algorithm-conventions'
	},
	'Abstract Equality Comparison': {
		url: 'https://262.ecma-international.org/7.0/#sec-abstract-equality-comparison'
	},
	'Abstract Relational Comparison': {
		url: 'https://262.ecma-international.org/7.0/#sec-abstract-relational-comparison'
	},
	AddRestrictedFunctionProperties: {
		url: 'https://262.ecma-international.org/7.0/#sec-addrestrictedfunctionproperties'
	},
	AdvanceStringIndex: {
		url: 'https://262.ecma-international.org/7.0/#sec-advancestringindex'
	},
	AllocateArrayBuffer: {
		url: 'https://262.ecma-international.org/7.0/#sec-allocatearraybuffer'
	},
	AllocateTypedArray: {
		url: 'https://262.ecma-international.org/7.0/#sec-allocatetypedarray'
	},
	AllocateTypedArrayBuffer: {
		url: 'https://262.ecma-international.org/7.0/#sec-allocatetypedarraybuffer'
	},
	ArrayCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-arraycreate'
	},
	ArraySetLength: {
		url: 'https://262.ecma-international.org/7.0/#sec-arraysetlength'
	},
	ArraySpeciesCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-arrayspeciescreate'
	},
	BlockDeclarationInstantiation: {
		url: 'https://262.ecma-international.org/7.0/#sec-blockdeclarationinstantiation'
	},
	BoundFunctionCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-boundfunctioncreate'
	},
	Call: {
		url: 'https://262.ecma-international.org/7.0/#sec-call'
	},
	Canonicalize: {
		url: 'https://262.ecma-international.org/7.0/#sec-runtime-semantics-canonicalize-ch'
	},
	CanonicalNumericIndexString: {
		url: 'https://262.ecma-international.org/7.0/#sec-canonicalnumericindexstring'
	},
	CharacterRange: {
		url: 'https://262.ecma-international.org/7.0/#sec-runtime-semantics-characterrange-abstract-operation'
	},
	CharacterRangeOrUnion: {
		url: 'https://262.ecma-international.org/7.0/#sec-runtime-semantics-characterrangeorunion-abstract-operation'
	},
	CharacterSetMatcher: {
		url: 'https://262.ecma-international.org/7.0/#sec-runtime-semantics-charactersetmatcher-abstract-operation'
	},
	CloneArrayBuffer: {
		url: 'https://262.ecma-international.org/7.0/#sec-clonearraybuffer'
	},
	CompletePropertyDescriptor: {
		url: 'https://262.ecma-international.org/7.0/#sec-completepropertydescriptor'
	},
	Completion: {
		url: 'https://262.ecma-international.org/7.0/#sec-completion-record-specification-type'
	},
	CompletionRecord: {
		url: 'https://262.ecma-international.org/7.0/#sec-completion-record-specification-type'
	},
	Construct: {
		url: 'https://262.ecma-international.org/7.0/#sec-construct'
	},
	CopyDataBlockBytes: {
		url: 'https://262.ecma-international.org/7.0/#sec-copydatablockbytes'
	},
	CreateArrayFromList: {
		url: 'https://262.ecma-international.org/7.0/#sec-createarrayfromlist'
	},
	CreateArrayIterator: {
		url: 'https://262.ecma-international.org/7.0/#sec-createarrayiterator'
	},
	CreateBuiltinFunction: {
		url: 'https://262.ecma-international.org/7.0/#sec-createbuiltinfunction'
	},
	CreateByteDataBlock: {
		url: 'https://262.ecma-international.org/7.0/#sec-createbytedatablock'
	},
	CreateDataProperty: {
		url: 'https://262.ecma-international.org/7.0/#sec-createdataproperty'
	},
	CreateDataPropertyOrThrow: {
		url: 'https://262.ecma-international.org/7.0/#sec-createdatapropertyorthrow'
	},
	CreateDynamicFunction: {
		url: 'https://262.ecma-international.org/7.0/#sec-createdynamicfunction'
	},
	CreateHTML: {
		url: 'https://262.ecma-international.org/7.0/#sec-createhtml'
	},
	CreateIntrinsics: {
		url: 'https://262.ecma-international.org/7.0/#sec-createintrinsics'
	},
	CreateIterResultObject: {
		url: 'https://262.ecma-international.org/7.0/#sec-createiterresultobject'
	},
	CreateListFromArrayLike: {
		url: 'https://262.ecma-international.org/7.0/#sec-createlistfromarraylike'
	},
	CreateListIterator: {
		url: 'https://262.ecma-international.org/7.0/#sec-createlistiterator'
	},
	CreateMapIterator: {
		url: 'https://262.ecma-international.org/7.0/#sec-createmapiterator'
	},
	CreateMappedArgumentsObject: {
		url: 'https://262.ecma-international.org/7.0/#sec-createmappedargumentsobject'
	},
	CreateMethodProperty: {
		url: 'https://262.ecma-international.org/7.0/#sec-createmethodproperty'
	},
	CreatePerIterationEnvironment: {
		url: 'https://262.ecma-international.org/7.0/#sec-createperiterationenvironment'
	},
	CreateRealm: {
		url: 'https://262.ecma-international.org/7.0/#sec-createrealm'
	},
	CreateResolvingFunctions: {
		url: 'https://262.ecma-international.org/7.0/#sec-createresolvingfunctions'
	},
	CreateSetIterator: {
		url: 'https://262.ecma-international.org/7.0/#sec-createsetiterator'
	},
	CreateStringIterator: {
		url: 'https://262.ecma-international.org/7.0/#sec-createstringiterator'
	},
	CreateUnmappedArgumentsObject: {
		url: 'https://262.ecma-international.org/7.0/#sec-createunmappedargumentsobject'
	},
	DateFromTime: {
		url: 'https://262.ecma-international.org/7.0/#sec-date-number'
	},
	Day: {
		url: 'https://262.ecma-international.org/7.0/#sec-day-number-and-time-within-day'
	},
	DayFromYear: {
		url: 'https://262.ecma-international.org/7.0/#sec-year-number'
	},
	DaylightSavingTA: {
		url: 'https://262.ecma-international.org/7.0/#sec-daylight-saving-time-adjustment'
	},
	DaysInYear: {
		url: 'https://262.ecma-international.org/7.0/#sec-year-number'
	},
	DayWithinYear: {
		url: 'https://262.ecma-international.org/7.0/#sec-month-number'
	},
	Decode: {
		url: 'https://262.ecma-international.org/7.0/#sec-decode'
	},
	DefinePropertyOrThrow: {
		url: 'https://262.ecma-international.org/7.0/#sec-definepropertyorthrow'
	},
	DeletePropertyOrThrow: {
		url: 'https://262.ecma-international.org/7.0/#sec-deletepropertyorthrow'
	},
	DetachArrayBuffer: {
		url: 'https://262.ecma-international.org/7.0/#sec-detacharraybuffer'
	},
	Encode: {
		url: 'https://262.ecma-international.org/7.0/#sec-encode'
	},
	EnqueueJob: {
		url: 'https://262.ecma-international.org/7.0/#sec-enqueuejob'
	},
	EnumerableOwnNames: {
		url: 'https://262.ecma-international.org/7.0/#sec-enumerableownnames'
	},
	EnumerateObjectProperties: {
		url: 'https://262.ecma-international.org/7.0/#sec-enumerate-object-properties'
	},
	EscapeRegExpPattern: {
		url: 'https://262.ecma-international.org/7.0/#sec-escaperegexppattern'
	},
	EvalDeclarationInstantiation: {
		url: 'https://262.ecma-international.org/7.0/#sec-evaldeclarationinstantiation'
	},
	EvaluateCall: {
		url: 'https://262.ecma-international.org/7.0/#sec-evaluatecall'
	},
	EvaluateDirectCall: {
		url: 'https://262.ecma-international.org/7.0/#sec-evaluatedirectcall'
	},
	EvaluateNew: {
		url: 'https://262.ecma-international.org/7.0/#sec-evaluatenew'
	},
	floor: {
		url: 'https://262.ecma-international.org/7.0/#sec-algorithm-conventions'
	},
	ForBodyEvaluation: {
		url: 'https://262.ecma-international.org/7.0/#sec-forbodyevaluation'
	},
	'ForIn/OfBodyEvaluation': {
		url: 'https://262.ecma-international.org/7.0/#sec-runtime-semantics-forin-div-ofbodyevaluation-lhs-stmt-iterator-lhskind-labelset'
	},
	'ForIn/OfHeadEvaluation': {
		url: 'https://262.ecma-international.org/7.0/#sec-runtime-semantics-forin-div-ofheadevaluation-tdznames-expr-iterationkind'
	},
	FromPropertyDescriptor: {
		url: 'https://262.ecma-international.org/7.0/#sec-frompropertydescriptor'
	},
	FulfillPromise: {
		url: 'https://262.ecma-international.org/7.0/#sec-fulfillpromise'
	},
	FunctionAllocate: {
		url: 'https://262.ecma-international.org/7.0/#sec-functionallocate'
	},
	FunctionCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-functioncreate'
	},
	FunctionDeclarationInstantiation: {
		url: 'https://262.ecma-international.org/7.0/#sec-functiondeclarationinstantiation'
	},
	FunctionInitialize: {
		url: 'https://262.ecma-international.org/7.0/#sec-functioninitialize'
	},
	GeneratorFunctionCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-generatorfunctioncreate'
	},
	GeneratorResume: {
		url: 'https://262.ecma-international.org/7.0/#sec-generatorresume'
	},
	GeneratorResumeAbrupt: {
		url: 'https://262.ecma-international.org/7.0/#sec-generatorresumeabrupt'
	},
	GeneratorStart: {
		url: 'https://262.ecma-international.org/7.0/#sec-generatorstart'
	},
	GeneratorValidate: {
		url: 'https://262.ecma-international.org/7.0/#sec-generatorvalidate'
	},
	GeneratorYield: {
		url: 'https://262.ecma-international.org/7.0/#sec-generatoryield'
	},
	Get: {
		url: 'https://262.ecma-international.org/7.0/#sec-get-o-p'
	},
	GetActiveScriptOrModule: {
		url: 'https://262.ecma-international.org/7.0/#sec-getactivescriptormodule'
	},
	GetFunctionRealm: {
		url: 'https://262.ecma-international.org/7.0/#sec-getfunctionrealm'
	},
	GetGlobalObject: {
		url: 'https://262.ecma-international.org/7.0/#sec-getglobalobject'
	},
	GetIdentifierReference: {
		url: 'https://262.ecma-international.org/7.0/#sec-getidentifierreference'
	},
	GetIterator: {
		url: 'https://262.ecma-international.org/7.0/#sec-getiterator'
	},
	GetMethod: {
		url: 'https://262.ecma-international.org/7.0/#sec-getmethod'
	},
	GetModuleNamespace: {
		url: 'https://262.ecma-international.org/7.0/#sec-getmodulenamespace'
	},
	GetNewTarget: {
		url: 'https://262.ecma-international.org/7.0/#sec-getnewtarget'
	},
	GetOwnPropertyKeys: {
		url: 'https://262.ecma-international.org/7.0/#sec-getownpropertykeys'
	},
	GetPrototypeFromConstructor: {
		url: 'https://262.ecma-international.org/7.0/#sec-getprototypefromconstructor'
	},
	GetSubstitution: {
		url: 'https://262.ecma-international.org/7.0/#sec-getsubstitution'
	},
	GetSuperConstructor: {
		url: 'https://262.ecma-international.org/7.0/#sec-getsuperconstructor'
	},
	GetTemplateObject: {
		url: 'https://262.ecma-international.org/7.0/#sec-gettemplateobject'
	},
	GetThisEnvironment: {
		url: 'https://262.ecma-international.org/7.0/#sec-getthisenvironment'
	},
	GetThisValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-getthisvalue'
	},
	GetV: {
		url: 'https://262.ecma-international.org/7.0/#sec-getv'
	},
	GetValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-getvalue'
	},
	GetValueFromBuffer: {
		url: 'https://262.ecma-international.org/7.0/#sec-getvaluefrombuffer'
	},
	GetViewValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-getviewvalue'
	},
	GlobalDeclarationInstantiation: {
		url: 'https://262.ecma-international.org/7.0/#sec-globaldeclarationinstantiation'
	},
	HasOwnProperty: {
		url: 'https://262.ecma-international.org/7.0/#sec-hasownproperty'
	},
	HasProperty: {
		url: 'https://262.ecma-international.org/7.0/#sec-hasproperty'
	},
	HourFromTime: {
		url: 'https://262.ecma-international.org/7.0/#sec-hours-minutes-second-and-milliseconds'
	},
	IfAbruptRejectPromise: {
		url: 'https://262.ecma-international.org/7.0/#sec-ifabruptrejectpromise'
	},
	ImportedLocalNames: {
		url: 'https://262.ecma-international.org/7.0/#sec-importedlocalnames'
	},
	InitializeBoundName: {
		url: 'https://262.ecma-international.org/7.0/#sec-initializeboundname'
	},
	InitializeHostDefinedRealm: {
		url: 'https://262.ecma-international.org/7.0/#sec-initializehostdefinedrealm'
	},
	InitializeReferencedBinding: {
		url: 'https://262.ecma-international.org/7.0/#sec-initializereferencedbinding'
	},
	InLeapYear: {
		url: 'https://262.ecma-international.org/7.0/#sec-year-number'
	},
	InstanceofOperator: {
		url: 'https://262.ecma-international.org/7.0/#sec-instanceofoperator'
	},
	IntegerIndexedElementGet: {
		url: 'https://262.ecma-international.org/7.0/#sec-integerindexedelementget'
	},
	IntegerIndexedElementSet: {
		url: 'https://262.ecma-international.org/7.0/#sec-integerindexedelementset'
	},
	IntegerIndexedObjectCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-integerindexedobjectcreate'
	},
	InternalizeJSONProperty: {
		url: 'https://262.ecma-international.org/7.0/#sec-internalizejsonproperty'
	},
	Invoke: {
		url: 'https://262.ecma-international.org/7.0/#sec-invoke'
	},
	IsAccessorDescriptor: {
		url: 'https://262.ecma-international.org/7.0/#sec-isaccessordescriptor'
	},
	IsAnonymousFunctionDefinition: {
		url: 'https://262.ecma-international.org/7.0/#sec-isanonymousfunctiondefinition'
	},
	IsArray: {
		url: 'https://262.ecma-international.org/7.0/#sec-isarray'
	},
	IsCallable: {
		url: 'https://262.ecma-international.org/7.0/#sec-iscallable'
	},
	IsCompatiblePropertyDescriptor: {
		url: 'https://262.ecma-international.org/7.0/#sec-iscompatiblepropertydescriptor'
	},
	IsConcatSpreadable: {
		url: 'https://262.ecma-international.org/7.0/#sec-isconcatspreadable'
	},
	IsConstructor: {
		url: 'https://262.ecma-international.org/7.0/#sec-isconstructor'
	},
	IsDataDescriptor: {
		url: 'https://262.ecma-international.org/7.0/#sec-isdatadescriptor'
	},
	IsDetachedBuffer: {
		url: 'https://262.ecma-international.org/7.0/#sec-isdetachedbuffer'
	},
	IsExtensible: {
		url: 'https://262.ecma-international.org/7.0/#sec-isextensible-o'
	},
	IsGenericDescriptor: {
		url: 'https://262.ecma-international.org/7.0/#sec-isgenericdescriptor'
	},
	IsInTailPosition: {
		url: 'https://262.ecma-international.org/7.0/#sec-isintailposition'
	},
	IsInteger: {
		url: 'https://262.ecma-international.org/7.0/#sec-isinteger'
	},
	IsLabelledFunction: {
		url: 'https://262.ecma-international.org/7.0/#sec-islabelledfunction'
	},
	IsPromise: {
		url: 'https://262.ecma-international.org/7.0/#sec-ispromise'
	},
	IsPropertyKey: {
		url: 'https://262.ecma-international.org/7.0/#sec-ispropertykey'
	},
	IsRegExp: {
		url: 'https://262.ecma-international.org/7.0/#sec-isregexp'
	},
	IsWordChar: {
		url: 'https://262.ecma-international.org/7.0/#sec-runtime-semantics-iswordchar-abstract-operation'
	},
	IterableToArrayLike: {
		url: 'https://262.ecma-international.org/7.0/#sec-iterabletoarraylike'
	},
	IteratorClose: {
		url: 'https://262.ecma-international.org/7.0/#sec-iteratorclose'
	},
	IteratorComplete: {
		url: 'https://262.ecma-international.org/7.0/#sec-iteratorcomplete'
	},
	IteratorNext: {
		url: 'https://262.ecma-international.org/7.0/#sec-iteratornext'
	},
	IteratorStep: {
		url: 'https://262.ecma-international.org/7.0/#sec-iteratorstep'
	},
	IteratorValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-iteratorvalue'
	},
	LocalTime: {
		url: 'https://262.ecma-international.org/7.0/#sec-localtime'
	},
	LoopContinues: {
		url: 'https://262.ecma-international.org/7.0/#sec-loopcontinues'
	},
	MakeArgGetter: {
		url: 'https://262.ecma-international.org/7.0/#sec-makearggetter'
	},
	MakeArgSetter: {
		url: 'https://262.ecma-international.org/7.0/#sec-makeargsetter'
	},
	MakeClassConstructor: {
		url: 'https://262.ecma-international.org/7.0/#sec-makeclassconstructor'
	},
	MakeConstructor: {
		url: 'https://262.ecma-international.org/7.0/#sec-makeconstructor'
	},
	MakeDate: {
		url: 'https://262.ecma-international.org/7.0/#sec-makedate'
	},
	MakeDay: {
		url: 'https://262.ecma-international.org/7.0/#sec-makeday'
	},
	MakeMethod: {
		url: 'https://262.ecma-international.org/7.0/#sec-makemethod'
	},
	MakeSuperPropertyReference: {
		url: 'https://262.ecma-international.org/7.0/#sec-makesuperpropertyreference'
	},
	MakeTime: {
		url: 'https://262.ecma-international.org/7.0/#sec-maketime'
	},
	max: {
		url: 'https://262.ecma-international.org/7.0/#sec-algorithm-conventions'
	},
	min: {
		url: 'https://262.ecma-international.org/7.0/#sec-algorithm-conventions'
	},
	MinFromTime: {
		url: 'https://262.ecma-international.org/7.0/#sec-hours-minutes-second-and-milliseconds'
	},
	ModuleNamespaceCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-modulenamespacecreate'
	},
	modulo: {
		url: 'https://262.ecma-international.org/7.0/#sec-algorithm-conventions'
	},
	MonthFromTime: {
		url: 'https://262.ecma-international.org/7.0/#sec-month-number'
	},
	msFromTime: {
		url: 'https://262.ecma-international.org/7.0/#sec-hours-minutes-second-and-milliseconds'
	},
	NewDeclarativeEnvironment: {
		url: 'https://262.ecma-international.org/7.0/#sec-newdeclarativeenvironment'
	},
	NewFunctionEnvironment: {
		url: 'https://262.ecma-international.org/7.0/#sec-newfunctionenvironment'
	},
	NewGlobalEnvironment: {
		url: 'https://262.ecma-international.org/7.0/#sec-newglobalenvironment'
	},
	NewModuleEnvironment: {
		url: 'https://262.ecma-international.org/7.0/#sec-newmoduleenvironment'
	},
	NewObjectEnvironment: {
		url: 'https://262.ecma-international.org/7.0/#sec-newobjectenvironment'
	},
	NewPromiseCapability: {
		url: 'https://262.ecma-international.org/7.0/#sec-newpromisecapability'
	},
	NextJob: {
		url: 'https://262.ecma-international.org/7.0/#sec-nextjob-result'
	},
	NormalCompletion: {
		url: 'https://262.ecma-international.org/7.0/#sec-normalcompletion'
	},
	ObjectCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-objectcreate'
	},
	ObjectDefineProperties: {
		url: 'https://262.ecma-international.org/7.0/#sec-objectdefineproperties'
	},
	OrdinaryCallBindThis: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinarycallbindthis'
	},
	OrdinaryCallEvaluateBody: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinarycallevaluatebody'
	},
	OrdinaryCreateFromConstructor: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinarycreatefromconstructor'
	},
	OrdinaryDefineOwnProperty: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinarydefineownproperty'
	},
	OrdinaryDelete: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinarydelete'
	},
	OrdinaryGet: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinaryget'
	},
	OrdinaryGetOwnProperty: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinarygetownproperty'
	},
	OrdinaryGetPrototypeOf: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinarygetprototypeof'
	},
	OrdinaryHasInstance: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinaryhasinstance'
	},
	OrdinaryHasProperty: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinaryhasproperty'
	},
	OrdinaryIsExtensible: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinaryisextensible'
	},
	OrdinaryOwnPropertyKeys: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinaryownpropertykeys'
	},
	OrdinaryPreventExtensions: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinarypreventextensions'
	},
	OrdinarySet: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinaryset'
	},
	OrdinarySetPrototypeOf: {
		url: 'https://262.ecma-international.org/7.0/#sec-ordinarysetprototypeof'
	},
	ParseModule: {
		url: 'https://262.ecma-international.org/7.0/#sec-parsemodule'
	},
	ParseScript: {
		url: 'https://262.ecma-international.org/7.0/#sec-parse-script'
	},
	PerformEval: {
		url: 'https://262.ecma-international.org/7.0/#sec-performeval'
	},
	PerformPromiseAll: {
		url: 'https://262.ecma-international.org/7.0/#sec-performpromiseall'
	},
	PerformPromiseRace: {
		url: 'https://262.ecma-international.org/7.0/#sec-performpromiserace'
	},
	PerformPromiseThen: {
		url: 'https://262.ecma-international.org/7.0/#sec-performpromisethen'
	},
	PrepareForOrdinaryCall: {
		url: 'https://262.ecma-international.org/7.0/#sec-prepareforordinarycall'
	},
	PrepareForTailCall: {
		url: 'https://262.ecma-international.org/7.0/#sec-preparefortailcall'
	},
	PromiseReactionJob: {
		url: 'https://262.ecma-international.org/7.0/#sec-promisereactionjob'
	},
	PromiseResolveThenableJob: {
		url: 'https://262.ecma-international.org/7.0/#sec-promiseresolvethenablejob'
	},
	ProxyCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-proxycreate'
	},
	PutValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-putvalue'
	},
	QuoteJSONString: {
		url: 'https://262.ecma-international.org/7.0/#sec-quotejsonstring'
	},
	RegExpAlloc: {
		url: 'https://262.ecma-international.org/7.0/#sec-regexpalloc'
	},
	RegExpBuiltinExec: {
		url: 'https://262.ecma-international.org/7.0/#sec-regexpbuiltinexec'
	},
	RegExpCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-regexpcreate'
	},
	RegExpExec: {
		url: 'https://262.ecma-international.org/7.0/#sec-regexpexec'
	},
	RegExpInitialize: {
		url: 'https://262.ecma-international.org/7.0/#sec-regexpinitialize'
	},
	RejectPromise: {
		url: 'https://262.ecma-international.org/7.0/#sec-rejectpromise'
	},
	RepeatMatcher: {
		url: 'https://262.ecma-international.org/7.0/#sec-runtime-semantics-repeatmatcher-abstract-operation'
	},
	RequireObjectCoercible: {
		url: 'https://262.ecma-international.org/7.0/#sec-requireobjectcoercible'
	},
	ResolveBinding: {
		url: 'https://262.ecma-international.org/7.0/#sec-resolvebinding'
	},
	ResolveThisBinding: {
		url: 'https://262.ecma-international.org/7.0/#sec-resolvethisbinding'
	},
	ReturnIfAbrupt: {
		url: 'https://262.ecma-international.org/7.0/#sec-returnifabrupt'
	},
	SameValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-samevalue'
	},
	SameValueNonNumber: {
		url: 'https://262.ecma-international.org/7.0/#sec-samevaluenonnumber'
	},
	SameValueZero: {
		url: 'https://262.ecma-international.org/7.0/#sec-samevaluezero'
	},
	ScriptEvaluation: {
		url: 'https://262.ecma-international.org/7.0/#sec-runtime-semantics-scriptevaluation'
	},
	ScriptEvaluationJob: {
		url: 'https://262.ecma-international.org/7.0/#sec-scriptevaluationjob'
	},
	SecFromTime: {
		url: 'https://262.ecma-international.org/7.0/#sec-hours-minutes-second-and-milliseconds'
	},
	SerializeJSONArray: {
		url: 'https://262.ecma-international.org/7.0/#sec-serializejsonarray'
	},
	SerializeJSONObject: {
		url: 'https://262.ecma-international.org/7.0/#sec-serializejsonobject'
	},
	SerializeJSONProperty: {
		url: 'https://262.ecma-international.org/7.0/#sec-serializejsonproperty'
	},
	Set: {
		url: 'https://262.ecma-international.org/7.0/#sec-set-o-p-v-throw'
	},
	SetDefaultGlobalBindings: {
		url: 'https://262.ecma-international.org/7.0/#sec-setdefaultglobalbindings'
	},
	SetFunctionName: {
		url: 'https://262.ecma-international.org/7.0/#sec-setfunctionname'
	},
	SetIntegrityLevel: {
		url: 'https://262.ecma-international.org/7.0/#sec-setintegritylevel'
	},
	SetRealmGlobalObject: {
		url: 'https://262.ecma-international.org/7.0/#sec-setrealmglobalobject'
	},
	SetValueInBuffer: {
		url: 'https://262.ecma-international.org/7.0/#sec-setvalueinbuffer'
	},
	SetViewValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-setviewvalue'
	},
	SortCompare: {
		url: 'https://262.ecma-international.org/7.0/#sec-sortcompare'
	},
	SpeciesConstructor: {
		url: 'https://262.ecma-international.org/7.0/#sec-speciesconstructor'
	},
	SplitMatch: {
		url: 'https://262.ecma-international.org/7.0/#sec-splitmatch'
	},
	'Strict Equality Comparison': {
		url: 'https://262.ecma-international.org/7.0/#sec-strict-equality-comparison'
	},
	StringCreate: {
		url: 'https://262.ecma-international.org/7.0/#sec-stringcreate'
	},
	SymbolDescriptiveString: {
		url: 'https://262.ecma-international.org/7.0/#sec-symboldescriptivestring'
	},
	TestIntegrityLevel: {
		url: 'https://262.ecma-international.org/7.0/#sec-testintegritylevel'
	},
	thisBooleanValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-thisbooleanvalue'
	},
	thisNumberValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-properties-of-the-number-prototype-object'
	},
	thisStringValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-properties-of-the-string-prototype-object'
	},
	thisTimeValue: {
		url: 'https://262.ecma-international.org/7.0/#sec-properties-of-the-date-prototype-object'
	},
	TimeClip: {
		url: 'https://262.ecma-international.org/7.0/#sec-timeclip'
	},
	TimeFromYear: {
		url: 'https://262.ecma-international.org/7.0/#sec-year-number'
	},
	TimeWithinDay: {
		url: 'https://262.ecma-international.org/7.0/#sec-day-number-and-time-within-day'
	},
	ToBoolean: {
		url: 'https://262.ecma-international.org/7.0/#sec-toboolean'
	},
	ToDateString: {
		url: 'https://262.ecma-international.org/7.0/#sec-todatestring'
	},
	ToInt16: {
		url: 'https://262.ecma-international.org/7.0/#sec-toint16'
	},
	ToInt32: {
		url: 'https://262.ecma-international.org/7.0/#sec-toint32'
	},
	ToInt8: {
		url: 'https://262.ecma-international.org/7.0/#sec-toint8'
	},
	ToInteger: {
		url: 'https://262.ecma-international.org/7.0/#sec-tointeger'
	},
	ToLength: {
		url: 'https://262.ecma-international.org/7.0/#sec-tolength'
	},
	ToNumber: {
		url: 'https://262.ecma-international.org/7.0/#sec-tonumber'
	},
	ToObject: {
		url: 'https://262.ecma-international.org/7.0/#sec-toobject'
	},
	TopLevelModuleEvaluationJob: {
		url: 'https://262.ecma-international.org/7.0/#sec-toplevelmoduleevaluationjob'
	},
	ToPrimitive: {
		url: 'https://262.ecma-international.org/7.0/#sec-toprimitive'
	},
	ToPropertyDescriptor: {
		url: 'https://262.ecma-international.org/7.0/#sec-topropertydescriptor'
	},
	ToPropertyKey: {
		url: 'https://262.ecma-international.org/7.0/#sec-topropertykey'
	},
	ToString: {
		url: 'https://262.ecma-international.org/7.0/#sec-tostring'
	},
	'ToString Applied to the Number Type': {
		url: 'https://262.ecma-international.org/7.0/#sec-tostring-applied-to-the-number-type'
	},
	ToUint16: {
		url: 'https://262.ecma-international.org/7.0/#sec-touint16'
	},
	ToUint32: {
		url: 'https://262.ecma-international.org/7.0/#sec-touint32'
	},
	ToUint8: {
		url: 'https://262.ecma-international.org/7.0/#sec-touint8'
	},
	ToUint8Clamp: {
		url: 'https://262.ecma-international.org/7.0/#sec-touint8clamp'
	},
	TriggerPromiseReactions: {
		url: 'https://262.ecma-international.org/7.0/#sec-triggerpromisereactions'
	},
	Type: {
		url: 'https://262.ecma-international.org/7.0/#sec-ecmascript-data-types-and-values'
	},
	TypedArrayCreate: {
		url: 'https://262.ecma-international.org/7.0/#typedarray-create'
	},
	TypedArraySpeciesCreate: {
		url: 'https://262.ecma-international.org/7.0/#typedarray-species-create'
	},
	UpdateEmpty: {
		url: 'https://262.ecma-international.org/7.0/#sec-updateempty'
	},
	UTC: {
		url: 'https://262.ecma-international.org/7.0/#sec-utc-t'
	},
	UTF16Decode: {
		url: 'https://262.ecma-international.org/7.0/#sec-utf16decode'
	},
	UTF16Encoding: {
		url: 'https://262.ecma-international.org/7.0/#sec-utf16encoding'
	},
	ValidateAndApplyPropertyDescriptor: {
		url: 'https://262.ecma-international.org/7.0/#sec-validateandapplypropertydescriptor'
	},
	ValidateTypedArray: {
		url: 'https://262.ecma-international.org/7.0/#sec-validatetypedarray'
	},
	WeekDay: {
		url: 'https://262.ecma-international.org/7.0/#sec-week-day'
	},
	YearFromTime: {
		url: 'https://262.ecma-international.org/7.0/#sec-year-number'
	}
};
