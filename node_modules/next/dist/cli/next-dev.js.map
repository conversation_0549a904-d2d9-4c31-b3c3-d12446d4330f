{"version": 3, "sources": ["../../src/cli/next-dev.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport '../server/lib/cpu-profile'\nimport type { StartServerOptions } from '../server/lib/start-server'\nimport {\n  RESTART_EXIT_CODE,\n  getNodeDebugType,\n  getParsedDebugAddress,\n  getMaxOldSpaceSize,\n  getParsedNodeOptionsWithoutInspect,\n  printAndExit,\n  formatNodeOptions,\n  formatDebugAddress,\n} from '../server/lib/utils'\nimport * as Log from '../build/output/log'\nimport { getProjectDir } from '../lib/get-project-dir'\nimport { PHASE_DEVELOPMENT_SERVER } from '../shared/lib/constants'\nimport path from 'path'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { setGlobal, traceGlobals } from '../trace/shared'\nimport { Telemetry } from '../telemetry/storage'\nimport loadConfig from '../server/config'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport { fileExists, FileType } from '../lib/file-exists'\nimport { getNpxCommand } from '../lib/helpers/get-npx-command'\nimport { createSelfSignedCertificate } from '../lib/mkcert'\nimport type { SelfSignedCertificate } from '../lib/mkcert'\nimport uploadTrace from '../trace/upload-trace'\nimport { initialEnv } from '@next/env'\nimport { fork } from 'child_process'\nimport type { ChildProcess } from 'child_process'\nimport {\n  getReservedPortExplanation,\n  isPortIsReserved,\n} from '../lib/helpers/get-reserved-port'\nimport os from 'os'\nimport { once } from 'node:events'\nimport { clearTimeout } from 'timers'\nimport { flushAllTraces, trace } from '../trace'\nimport { traceId } from '../trace/shared'\n\nexport type NextDevOptions = {\n  disableSourceMaps: boolean\n  turbo?: boolean\n  turbopack?: boolean\n  port: number\n  hostname?: string\n  experimentalHttps?: boolean\n  experimentalHttpsKey?: string\n  experimentalHttpsCert?: string\n  experimentalHttpsCa?: string\n  experimentalUploadTrace?: string\n}\n\ntype PortSource = 'cli' | 'default' | 'env'\n\nlet dir: string\nlet child: undefined | ChildProcess\nlet config: NextConfigComplete\nlet isTurboSession = false\nlet traceUploadUrl: string\nlet sessionStopHandled = false\nlet sessionStarted = Date.now()\nlet sessionSpan = trace('next-dev')\n\n// How long should we wait for the child to cleanly exit after sending\n// SIGINT/SIGTERM to the child process before sending SIGKILL?\nconst CHILD_EXIT_TIMEOUT_MS = parseInt(\n  process.env.NEXT_EXIT_TIMEOUT_MS ?? '100',\n  10\n)\n\nconst handleSessionStop = async (signal: NodeJS.Signals | number | null) => {\n  if (signal != null && child?.pid) child.kill(signal)\n  if (sessionStopHandled) return\n  sessionStopHandled = true\n\n  if (\n    signal != null &&\n    child?.pid &&\n    child.exitCode === null &&\n    child.signalCode === null\n  ) {\n    let exitTimeout = setTimeout(() => {\n      child?.kill('SIGKILL')\n    }, CHILD_EXIT_TIMEOUT_MS)\n    await once(child, 'exit').catch(() => {})\n    clearTimeout(exitTimeout)\n  }\n\n  sessionSpan.stop()\n  await flushAllTraces({ end: true })\n\n  try {\n    const { eventCliSessionStopped } =\n      require('../telemetry/events/session-stopped') as typeof import('../telemetry/events/session-stopped')\n\n    config = config || (await loadConfig(PHASE_DEVELOPMENT_SERVER, dir))\n\n    let telemetry =\n      (traceGlobals.get('telemetry') as InstanceType<\n        typeof import('../telemetry/storage').Telemetry\n      >) ||\n      new Telemetry({\n        distDir: path.join(dir, config.distDir),\n      })\n\n    let pagesDir: boolean = !!traceGlobals.get('pagesDir')\n    let appDir: boolean = !!traceGlobals.get('appDir')\n\n    if (\n      typeof traceGlobals.get('pagesDir') === 'undefined' ||\n      typeof traceGlobals.get('appDir') === 'undefined'\n    ) {\n      const pagesResult = findPagesDir(dir)\n      appDir = !!pagesResult.appDir\n      pagesDir = !!pagesResult.pagesDir\n    }\n\n    telemetry.record(\n      eventCliSessionStopped({\n        cliCommand: 'dev',\n        turboFlag: isTurboSession,\n        durationMilliseconds: Date.now() - sessionStarted,\n        pagesDir,\n        appDir,\n      }),\n      true\n    )\n    telemetry.flushDetached('dev', dir)\n  } catch (_) {\n    // errors here aren't actionable so don't add\n    // noise to the output\n  }\n\n  if (traceUploadUrl) {\n    uploadTrace({\n      traceUploadUrl,\n      mode: 'dev',\n      projectDir: dir,\n      distDir: config.distDir,\n      isTurboSession,\n    })\n  }\n\n  // ensure we re-enable the terminal cursor before exiting\n  // the program, or the cursor could remain hidden\n  process.stdout.write('\\x1B[?25h')\n  process.stdout.write('\\n')\n  process.exit(0)\n}\n\nprocess.on('SIGINT', () => handleSessionStop('SIGINT'))\nprocess.on('SIGTERM', () => handleSessionStop('SIGTERM'))\n\n// exit event must be synchronous\nprocess.on('exit', () => child?.kill('SIGKILL'))\n\nconst nextDev = async (\n  options: NextDevOptions,\n  portSource: PortSource,\n  directory?: string\n) => {\n  dir = getProjectDir(process.env.NEXT_PRIVATE_DEV_DIR || directory)\n\n  // Check if pages dir exists and warn if not\n  if (!(await fileExists(dir, FileType.Directory))) {\n    printAndExit(`> No such directory exists as the project root: ${dir}`)\n  }\n\n  async function preflight(skipOnReboot: boolean) {\n    const { getPackageVersion, getDependencies } = (await Promise.resolve(\n      require('../lib/get-package-version') as typeof import('../lib/get-package-version')\n    )) as typeof import('../lib/get-package-version')\n\n    const [sassVersion, nodeSassVersion] = await Promise.all([\n      getPackageVersion({ cwd: dir, name: 'sass' }),\n      getPackageVersion({ cwd: dir, name: 'node-sass' }),\n    ])\n    if (sassVersion && nodeSassVersion) {\n      Log.warn(\n        'Your project has both `sass` and `node-sass` installed as dependencies, but should only use one or the other. ' +\n          'Please remove the `node-sass` dependency from your project. ' +\n          ' Read more: https://nextjs.org/docs/messages/duplicate-sass'\n      )\n    }\n\n    if (!skipOnReboot) {\n      const { dependencies, devDependencies } = await getDependencies({\n        cwd: dir,\n      })\n\n      // Warn if @next/font is installed as a dependency. Ignore `workspace:*` to not warn in the Next.js monorepo.\n      if (\n        dependencies['@next/font'] ||\n        (devDependencies['@next/font'] &&\n          devDependencies['@next/font'] !== 'workspace:*')\n      ) {\n        const command = getNpxCommand(dir)\n        Log.warn(\n          'Your project has `@next/font` installed as a dependency, please use the built-in `next/font` instead. ' +\n            'The `@next/font` package will be removed in Next.js 14. ' +\n            `You can migrate by running \\`${command} @next/codemod@latest built-in-next-font .\\`. Read more: https://nextjs.org/docs/messages/built-in-next-font`\n        )\n      }\n    }\n  }\n\n  let port = options.port\n\n  if (isPortIsReserved(port)) {\n    printAndExit(getReservedPortExplanation(port), 1)\n  }\n\n  // If neither --port nor PORT were specified, it's okay to retry new ports.\n  const allowRetry = portSource === 'default'\n\n  // We do not set a default host value here to prevent breaking\n  // some set-ups that rely on listening on other interfaces\n  const host = options.hostname\n\n  config = await loadConfig(PHASE_DEVELOPMENT_SERVER, dir)\n\n  if (\n    options.experimentalUploadTrace &&\n    !process.env.NEXT_TRACE_UPLOAD_DISABLED\n  ) {\n    traceUploadUrl = options.experimentalUploadTrace\n  }\n\n  const devServerOptions: StartServerOptions = {\n    dir,\n    port,\n    allowRetry,\n    isDev: true,\n    hostname: host,\n  }\n\n  const isTurbopack = Boolean(\n    options.turbo || options.turbopack || process.env.IS_TURBOPACK_TEST\n  )\n  if (isTurbopack) {\n    process.env.TURBOPACK = '1'\n  }\n\n  isTurboSession = isTurbopack\n\n  const distDir = path.join(dir, config.distDir ?? '.next')\n  setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n  setGlobal('distDir', distDir)\n\n  const startServerPath = require.resolve('../server/lib/start-server')\n\n  async function startServer(startServerOptions: StartServerOptions) {\n    return new Promise<void>((resolve) => {\n      let resolved = false\n      const defaultEnv = (initialEnv || process.env) as typeof process.env\n\n      const nodeOptions = getParsedNodeOptionsWithoutInspect()\n      const nodeDebugType = getNodeDebugType()\n\n      let maxOldSpaceSize: string | number | undefined = getMaxOldSpaceSize()\n      if (!maxOldSpaceSize && !process.env.NEXT_DISABLE_MEM_OVERRIDE) {\n        const totalMem = os.totalmem()\n        const totalMemInMB = Math.floor(totalMem / 1024 / 1024)\n        maxOldSpaceSize = Math.floor(totalMemInMB * 0.5).toString()\n\n        nodeOptions['max-old-space-size'] = maxOldSpaceSize\n\n        // Ensure the max_old_space_size is not also set.\n        delete nodeOptions['max_old_space_size']\n      }\n\n      if (options.disableSourceMaps) {\n        delete nodeOptions['enable-source-maps']\n      } else {\n        nodeOptions['enable-source-maps'] = true\n      }\n\n      if (nodeDebugType) {\n        const address = getParsedDebugAddress()\n        address.port = address.port + 1\n        nodeOptions[nodeDebugType] = formatDebugAddress(address)\n      }\n\n      child = fork(startServerPath, {\n        stdio: 'inherit',\n        env: {\n          ...defaultEnv,\n          ...(isTurbopack ? { TURBOPACK: '1' } : undefined),\n          NEXT_PRIVATE_WORKER: '1',\n          NEXT_PRIVATE_TRACE_ID: traceId,\n          NODE_EXTRA_CA_CERTS: startServerOptions.selfSignedCertificate\n            ? startServerOptions.selfSignedCertificate.rootCA\n            : defaultEnv.NODE_EXTRA_CA_CERTS,\n          NODE_OPTIONS: formatNodeOptions(nodeOptions),\n          // There is a node.js bug on MacOS which causes closing file watchers to be really slow.\n          // This limits the number of watchers to mitigate the issue.\n          // https://github.com/nodejs/node/issues/29949\n          WATCHPACK_WATCHER_LIMIT:\n            os.platform() === 'darwin' ? '20' : undefined,\n        },\n      })\n\n      child.on('message', (msg: any) => {\n        if (msg && typeof msg === 'object') {\n          if (msg.nextWorkerReady) {\n            child?.send({ nextWorkerOptions: startServerOptions })\n          } else if (msg.nextServerReady && !resolved) {\n            if (msg.port) {\n              // Store the used port in case a random one was selected, so that\n              // it can be re-used on automatic dev server restarts.\n              port = parseInt(msg.port, 10)\n            }\n\n            resolved = true\n            resolve()\n          }\n        }\n      })\n\n      child.on('exit', async (code, signal) => {\n        if (sessionStopHandled || signal) {\n          return\n        }\n        if (code === RESTART_EXIT_CODE) {\n          // Starting the dev server will overwrite the `.next/trace` file, so we\n          // must upload the existing contents before restarting the server to\n          // preserve the metrics.\n          if (traceUploadUrl) {\n            uploadTrace({\n              traceUploadUrl,\n              mode: 'dev',\n              projectDir: dir,\n              distDir: config.distDir,\n              isTurboSession,\n              sync: true,\n            })\n          }\n\n          return startServer({ ...startServerOptions, port })\n        }\n        // Call handler (e.g. upload telemetry). Don't try to send a signal to\n        // the child, as it has already exited.\n        await handleSessionStop(/* signal */ null)\n      })\n    })\n  }\n\n  const runDevServer = async (reboot: boolean) => {\n    try {\n      if (!!options.experimentalHttps) {\n        Log.warn(\n          'Self-signed certificates are currently an experimental feature, use with caution.'\n        )\n\n        let certificate: SelfSignedCertificate | undefined\n\n        const key = options.experimentalHttpsKey\n        const cert = options.experimentalHttpsCert\n        const rootCA = options.experimentalHttpsCa\n\n        if (key && cert) {\n          certificate = {\n            key: path.resolve(key),\n            cert: path.resolve(cert),\n            rootCA: rootCA ? path.resolve(rootCA) : undefined,\n          }\n        } else {\n          certificate = await createSelfSignedCertificate(host)\n        }\n\n        await startServer({\n          ...devServerOptions,\n          selfSignedCertificate: certificate,\n        })\n      } else {\n        await startServer(devServerOptions)\n      }\n\n      await preflight(reboot)\n    } catch (err) {\n      console.error(err)\n      process.exit(1)\n    }\n  }\n\n  await runDevServer(false)\n}\n\nexport { nextDev }\n"], "names": ["nextDev", "dir", "child", "config", "isTurboSession", "traceUploadUrl", "sessionStopHandled", "sessionStarted", "Date", "now", "sessionSpan", "trace", "CHILD_EXIT_TIMEOUT_MS", "parseInt", "process", "env", "NEXT_EXIT_TIMEOUT_MS", "handleSessionStop", "signal", "pid", "kill", "exitCode", "signalCode", "exitTimeout", "setTimeout", "once", "catch", "clearTimeout", "stop", "flushAllTraces", "end", "eventCliSessionStopped", "require", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "telemetry", "traceGlobals", "get", "Telemetry", "distDir", "path", "join", "pagesDir", "appDir", "pagesResult", "findPagesDir", "record", "cliCommand", "turboFlag", "durationMilliseconds", "flushDetached", "_", "uploadTrace", "mode", "projectDir", "stdout", "write", "exit", "on", "options", "portSource", "directory", "getProjectDir", "NEXT_PRIVATE_DEV_DIR", "fileExists", "FileType", "Directory", "printAndExit", "preflight", "skipOnReb<PERSON>", "getPackageVersion", "getDependencies", "Promise", "resolve", "sassVersion", "nodeSassVersion", "all", "cwd", "name", "Log", "warn", "dependencies", "devDependencies", "command", "getNpxCommand", "port", "isPortIsReserved", "getReservedPortExplanation", "allowRetry", "host", "hostname", "experimentalUploadTrace", "NEXT_TRACE_UPLOAD_DISABLED", "devServerOptions", "isDev", "isTurbopack", "Boolean", "turbo", "turbopack", "IS_TURBOPACK_TEST", "TURBOPACK", "setGlobal", "startServerPath", "startServer", "startServerOptions", "resolved", "defaultEnv", "initialEnv", "nodeOptions", "getParsedNodeOptionsWithoutInspect", "nodeDebugType", "getNodeDebugType", "maxOldSpaceSize", "getMaxOldSpaceSize", "NEXT_DISABLE_MEM_OVERRIDE", "totalMem", "os", "totalmem", "totalMemInMB", "Math", "floor", "toString", "disableSourceMaps", "address", "getParsedDebugAddress", "formatDebugAddress", "fork", "stdio", "undefined", "NEXT_PRIVATE_WORKER", "NEXT_PRIVATE_TRACE_ID", "traceId", "NODE_EXTRA_CA_CERTS", "selfSignedCertificate", "rootCA", "NODE_OPTIONS", "formatNodeOptions", "WATCHPACK_WATCHER_LIMIT", "platform", "msg", "nextWorkerReady", "send", "nextWorkerOptions", "nextServerReady", "code", "RESTART_EXIT_CODE", "sync", "runDevServer", "reboot", "experimentalHttps", "certificate", "key", "experimentalHttpsKey", "cert", "experimentalHttpsCert", "experimentalHttpsCa", "createSelfSignedCertificate", "err", "console", "error"], "mappings": ";;;;;+BAsYSA;;;eAAAA;;;QApYF;uBAWA;6DACc;+BACS;2BACW;6DACxB;wBAEuB;yBACd;+DACH;8BACM;4BACQ;+BACP;wBACc;oEAEpB;qBACG;+BACN;iCAKd;2DACQ;4BACM;wBACQ;uBACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBtC,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC,iBAAiB;AACrB,IAAIC;AACJ,IAAIC,qBAAqB;AACzB,IAAIC,iBAAiBC,KAAKC,GAAG;AAC7B,IAAIC,cAAcC,IAAAA,YAAK,EAAC;AAExB,sEAAsE;AACtE,8DAA8D;AAC9D,MAAMC,wBAAwBC,SAC5BC,QAAQC,GAAG,CAACC,oBAAoB,IAAI,OACpC;AAGF,MAAMC,oBAAoB,OAAOC;IAC/B,IAAIA,UAAU,SAAQhB,yBAAAA,MAAOiB,GAAG,GAAEjB,MAAMkB,IAAI,CAACF;IAC7C,IAAIZ,oBAAoB;IACxBA,qBAAqB;IAErB,IACEY,UAAU,SACVhB,yBAAAA,MAAOiB,GAAG,KACVjB,MAAMmB,QAAQ,KAAK,QACnBnB,MAAMoB,UAAU,KAAK,MACrB;QACA,IAAIC,cAAcC,WAAW;YAC3BtB,yBAAAA,MAAOkB,IAAI,CAAC;QACd,GAAGR;QACH,MAAMa,IAAAA,gBAAI,EAACvB,OAAO,QAAQwB,KAAK,CAAC,KAAO;QACvCC,IAAAA,oBAAY,EAACJ;IACf;IAEAb,YAAYkB,IAAI;IAChB,MAAMC,IAAAA,qBAAc,EAAC;QAAEC,KAAK;IAAK;IAEjC,IAAI;QACF,MAAM,EAAEC,sBAAsB,EAAE,GAC9BC,QAAQ;QAEV7B,SAASA,UAAW,MAAM8B,IAAAA,eAAU,EAACC,mCAAwB,EAAEjC;QAE/D,IAAIkC,YACF,AAACC,oBAAY,CAACC,GAAG,CAAC,gBAGlB,IAAIC,kBAAS,CAAC;YACZC,SAASC,aAAI,CAACC,IAAI,CAACxC,KAAKE,OAAOoC,OAAO;QACxC;QAEF,IAAIG,WAAoB,CAAC,CAACN,oBAAY,CAACC,GAAG,CAAC;QAC3C,IAAIM,SAAkB,CAAC,CAACP,oBAAY,CAACC,GAAG,CAAC;QAEzC,IACE,OAAOD,oBAAY,CAACC,GAAG,CAAC,gBAAgB,eACxC,OAAOD,oBAAY,CAACC,GAAG,CAAC,cAAc,aACtC;YACA,MAAMO,cAAcC,IAAAA,0BAAY,EAAC5C;YACjC0C,SAAS,CAAC,CAACC,YAAYD,MAAM;YAC7BD,WAAW,CAAC,CAACE,YAAYF,QAAQ;QACnC;QAEAP,UAAUW,MAAM,CACdf,uBAAuB;YACrBgB,YAAY;YACZC,WAAW5C;YACX6C,sBAAsBzC,KAAKC,GAAG,KAAKF;YACnCmC;YACAC;QACF,IACA;QAEFR,UAAUe,aAAa,CAAC,OAAOjD;IACjC,EAAE,OAAOkD,GAAG;IACV,6CAA6C;IAC7C,sBAAsB;IACxB;IAEA,IAAI9C,gBAAgB;QAClB+C,IAAAA,oBAAW,EAAC;YACV/C;YACAgD,MAAM;YACNC,YAAYrD;YACZsC,SAASpC,OAAOoC,OAAO;YACvBnC;QACF;IACF;IAEA,yDAAyD;IACzD,iDAAiD;IACjDU,QAAQyC,MAAM,CAACC,KAAK,CAAC;IACrB1C,QAAQyC,MAAM,CAACC,KAAK,CAAC;IACrB1C,QAAQ2C,IAAI,CAAC;AACf;AAEA3C,QAAQ4C,EAAE,CAAC,UAAU,IAAMzC,kBAAkB;AAC7CH,QAAQ4C,EAAE,CAAC,WAAW,IAAMzC,kBAAkB;AAE9C,iCAAiC;AACjCH,QAAQ4C,EAAE,CAAC,QAAQ,IAAMxD,yBAAAA,MAAOkB,IAAI,CAAC;AAErC,MAAMpB,UAAU,OACd2D,SACAC,YACAC;IAEA5D,MAAM6D,IAAAA,4BAAa,EAAChD,QAAQC,GAAG,CAACgD,oBAAoB,IAAIF;IAExD,4CAA4C;IAC5C,IAAI,CAAE,MAAMG,IAAAA,sBAAU,EAAC/D,KAAKgE,oBAAQ,CAACC,SAAS,GAAI;QAChDC,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAElE,KAAK;IACvE;IAEA,eAAemE,UAAUC,YAAqB;QAC5C,MAAM,EAAEC,iBAAiB,EAAEC,eAAe,EAAE,GAAI,MAAMC,QAAQC,OAAO,CACnEzC,QAAQ;QAGV,MAAM,CAAC0C,aAAaC,gBAAgB,GAAG,MAAMH,QAAQI,GAAG,CAAC;YACvDN,kBAAkB;gBAAEO,KAAK5E;gBAAK6E,MAAM;YAAO;YAC3CR,kBAAkB;gBAAEO,KAAK5E;gBAAK6E,MAAM;YAAY;SACjD;QACD,IAAIJ,eAAeC,iBAAiB;YAClCI,KAAIC,IAAI,CACN,mHACE,iEACA;QAEN;QAEA,IAAI,CAACX,cAAc;YACjB,MAAM,EAAEY,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMX,gBAAgB;gBAC9DM,KAAK5E;YACP;YAEA,6GAA6G;YAC7G,IACEgF,YAAY,CAAC,aAAa,IACzBC,eAAe,CAAC,aAAa,IAC5BA,eAAe,CAAC,aAAa,KAAK,eACpC;gBACA,MAAMC,UAAUC,IAAAA,4BAAa,EAACnF;gBAC9B8E,KAAIC,IAAI,CACN,2GACE,6DACA,CAAC,6BAA6B,EAAEG,QAAQ,4GAA4G,CAAC;YAE3J;QACF;IACF;IAEA,IAAIE,OAAO1B,QAAQ0B,IAAI;IAEvB,IAAIC,IAAAA,iCAAgB,EAACD,OAAO;QAC1BlB,IAAAA,mBAAY,EAACoB,IAAAA,2CAA0B,EAACF,OAAO;IACjD;IAEA,2EAA2E;IAC3E,MAAMG,aAAa5B,eAAe;IAElC,8DAA8D;IAC9D,0DAA0D;IAC1D,MAAM6B,OAAO9B,QAAQ+B,QAAQ;IAE7BvF,SAAS,MAAM8B,IAAAA,eAAU,EAACC,mCAAwB,EAAEjC;IAEpD,IACE0D,QAAQgC,uBAAuB,IAC/B,CAAC7E,QAAQC,GAAG,CAAC6E,0BAA0B,EACvC;QACAvF,iBAAiBsD,QAAQgC,uBAAuB;IAClD;IAEA,MAAME,mBAAuC;QAC3C5F;QACAoF;QACAG;QACAM,OAAO;QACPJ,UAAUD;IACZ;IAEA,MAAMM,cAAcC,QAClBrC,QAAQsC,KAAK,IAAItC,QAAQuC,SAAS,IAAIpF,QAAQC,GAAG,CAACoF,iBAAiB;IAErE,IAAIJ,aAAa;QACfjF,QAAQC,GAAG,CAACqF,SAAS,GAAG;IAC1B;IAEAhG,iBAAiB2F;IAEjB,MAAMxD,UAAUC,aAAI,CAACC,IAAI,CAACxC,KAAKE,OAAOoC,OAAO,IAAI;IACjD8D,IAAAA,iBAAS,EAAC,SAASnE,mCAAwB;IAC3CmE,IAAAA,iBAAS,EAAC,WAAW9D;IAErB,MAAM+D,kBAAkBtE,QAAQyC,OAAO,CAAC;IAExC,eAAe8B,YAAYC,kBAAsC;QAC/D,OAAO,IAAIhC,QAAc,CAACC;YACxB,IAAIgC,WAAW;YACf,MAAMC,aAAcC,eAAU,IAAI7F,QAAQC,GAAG;YAE7C,MAAM6F,cAAcC,IAAAA,yCAAkC;YACtD,MAAMC,gBAAgBC,IAAAA,uBAAgB;YAEtC,IAAIC,kBAA+CC,IAAAA,yBAAkB;YACrE,IAAI,CAACD,mBAAmB,CAAClG,QAAQC,GAAG,CAACmG,yBAAyB,EAAE;gBAC9D,MAAMC,WAAWC,WAAE,CAACC,QAAQ;gBAC5B,MAAMC,eAAeC,KAAKC,KAAK,CAACL,WAAW,OAAO;gBAClDH,kBAAkBO,KAAKC,KAAK,CAACF,eAAe,KAAKG,QAAQ;gBAEzDb,WAAW,CAAC,qBAAqB,GAAGI;gBAEpC,iDAAiD;gBACjD,OAAOJ,WAAW,CAAC,qBAAqB;YAC1C;YAEA,IAAIjD,QAAQ+D,iBAAiB,EAAE;gBAC7B,OAAOd,WAAW,CAAC,qBAAqB;YAC1C,OAAO;gBACLA,WAAW,CAAC,qBAAqB,GAAG;YACtC;YAEA,IAAIE,eAAe;gBACjB,MAAMa,UAAUC,IAAAA,4BAAqB;gBACrCD,QAAQtC,IAAI,GAAGsC,QAAQtC,IAAI,GAAG;gBAC9BuB,WAAW,CAACE,cAAc,GAAGe,IAAAA,yBAAkB,EAACF;YAClD;YAEAzH,QAAQ4H,IAAAA,mBAAI,EAACxB,iBAAiB;gBAC5ByB,OAAO;gBACPhH,KAAK;oBACH,GAAG2F,UAAU;oBACb,GAAIX,cAAc;wBAAEK,WAAW;oBAAI,IAAI4B,SAAS;oBAChDC,qBAAqB;oBACrBC,uBAAuBC,eAAO;oBAC9BC,qBAAqB5B,mBAAmB6B,qBAAqB,GACzD7B,mBAAmB6B,qBAAqB,CAACC,MAAM,GAC/C5B,WAAW0B,mBAAmB;oBAClCG,cAAcC,IAAAA,wBAAiB,EAAC5B;oBAChC,wFAAwF;oBACxF,4DAA4D;oBAC5D,8CAA8C;oBAC9C6B,yBACErB,WAAE,CAACsB,QAAQ,OAAO,WAAW,OAAOV;gBACxC;YACF;YAEA9H,MAAMwD,EAAE,CAAC,WAAW,CAACiF;gBACnB,IAAIA,OAAO,OAAOA,QAAQ,UAAU;oBAClC,IAAIA,IAAIC,eAAe,EAAE;wBACvB1I,yBAAAA,MAAO2I,IAAI,CAAC;4BAAEC,mBAAmBtC;wBAAmB;oBACtD,OAAO,IAAImC,IAAII,eAAe,IAAI,CAACtC,UAAU;wBAC3C,IAAIkC,IAAItD,IAAI,EAAE;4BACZ,iEAAiE;4BACjE,sDAAsD;4BACtDA,OAAOxE,SAAS8H,IAAItD,IAAI,EAAE;wBAC5B;wBAEAoB,WAAW;wBACXhC;oBACF;gBACF;YACF;YAEAvE,MAAMwD,EAAE,CAAC,QAAQ,OAAOsF,MAAM9H;gBAC5B,IAAIZ,sBAAsBY,QAAQ;oBAChC;gBACF;gBACA,IAAI8H,SAASC,wBAAiB,EAAE;oBAC9B,uEAAuE;oBACvE,oEAAoE;oBACpE,wBAAwB;oBACxB,IAAI5I,gBAAgB;wBAClB+C,IAAAA,oBAAW,EAAC;4BACV/C;4BACAgD,MAAM;4BACNC,YAAYrD;4BACZsC,SAASpC,OAAOoC,OAAO;4BACvBnC;4BACA8I,MAAM;wBACR;oBACF;oBAEA,OAAO3C,YAAY;wBAAE,GAAGC,kBAAkB;wBAAEnB;oBAAK;gBACnD;gBACA,sEAAsE;gBACtE,uCAAuC;gBACvC,MAAMpE,kBAAkB,UAAU,GAAG;YACvC;QACF;IACF;IAEA,MAAMkI,eAAe,OAAOC;QAC1B,IAAI;YACF,IAAI,CAAC,CAACzF,QAAQ0F,iBAAiB,EAAE;gBAC/BtE,KAAIC,IAAI,CACN;gBAGF,IAAIsE;gBAEJ,MAAMC,MAAM5F,QAAQ6F,oBAAoB;gBACxC,MAAMC,OAAO9F,QAAQ+F,qBAAqB;gBAC1C,MAAMpB,SAAS3E,QAAQgG,mBAAmB;gBAE1C,IAAIJ,OAAOE,MAAM;oBACfH,cAAc;wBACZC,KAAK/G,aAAI,CAACiC,OAAO,CAAC8E;wBAClBE,MAAMjH,aAAI,CAACiC,OAAO,CAACgF;wBACnBnB,QAAQA,SAAS9F,aAAI,CAACiC,OAAO,CAAC6D,UAAUN;oBAC1C;gBACF,OAAO;oBACLsB,cAAc,MAAMM,IAAAA,mCAA2B,EAACnE;gBAClD;gBAEA,MAAMc,YAAY;oBAChB,GAAGV,gBAAgB;oBACnBwC,uBAAuBiB;gBACzB;YACF,OAAO;gBACL,MAAM/C,YAAYV;YACpB;YAEA,MAAMzB,UAAUgF;QAClB,EAAE,OAAOS,KAAK;YACZC,QAAQC,KAAK,CAACF;YACd/I,QAAQ2C,IAAI,CAAC;QACf;IACF;IAEA,MAAM0F,aAAa;AACrB", "ignoreList": [0]}