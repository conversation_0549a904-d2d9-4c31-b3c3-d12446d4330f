{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/default-config.ts"], "sourcesContent": ["import { devices, type PlaywrightTestConfig } from '@playwright/test'\nimport type { NextOptionsConfig } from './next-options'\n\n/**\n * This is the default configuration generated by <PERSON><PERSON> as of v1.43.0 with some modifications.\n *\n * - the `testMatch` property is configured to match all `*.spec.js` or `*.spec.ts` files within the `app` and `pages` directories\n * - the `use` property is configured with a baseURL matching the expected dev server endpoint (http://127.0.0.1:3000)\n * - the `webserver` property is configured to run `next dev`.\n */\nexport const defaultPlaywrightConfig: PlaywrightTestConfig<NextOptionsConfig> =\n  {\n    testMatch: '{app,pages}/**/*.spec.{t,j}s',\n    fullyParallel: true,\n    forbidOnly: process.env.CI === 'true',\n    retries: process.env.CI === 'true' ? 2 : 0,\n    reporter: [['list'], ['html', { open: 'never' }]],\n    use: {\n      baseURL: 'http://127.0.0.1:3000',\n      trace: 'on-first-retry',\n    },\n    projects: [\n      {\n        name: 'chromium',\n        use: { ...devices['Desktop Chrome'] },\n      },\n\n      {\n        name: 'firefox',\n        use: { ...devices['Desktop Firefox'] },\n      },\n\n      {\n        name: 'webkit',\n        use: { ...devices['Desktop Safari'] },\n      },\n    ],\n    webServer: {\n      command: process.env.CI === 'true' ? 'next start' : 'next dev',\n      url: 'http://127.0.0.1:3000',\n      reuseExistingServer: process.env.CI !== 'true',\n    },\n  }\n"], "names": ["defaultPlaywrightConfig", "testMatch", "fullyParallel", "forbidOnly", "process", "env", "CI", "retries", "reporter", "open", "use", "baseURL", "trace", "projects", "name", "devices", "webServer", "command", "url", "reuseExistingServer"], "mappings": ";;;;+BAUaA;;;eAAAA;;;sBAVsC;AAU5C,MAAMA,0BACX;IACEC,WAAW;IACXC,eAAe;IACfC,YAAYC,QAAQC,GAAG,CAACC,EAAE,KAAK;IAC/BC,SAASH,QAAQC,GAAG,CAACC,EAAE,KAAK,SAAS,IAAI;IACzCE,UAAU;QAAC;YAAC;SAAO;QAAE;YAAC;YAAQ;gBAAEC,MAAM;YAAQ;SAAE;KAAC;IACjDC,KAAK;QACHC,SAAS;QACTC,OAAO;IACT;IACAC,UAAU;QACR;YACEC,MAAM;YACNJ,KAAK;gBAAE,GAAGK,aAAO,CAAC,iBAAiB;YAAC;QACtC;QAEA;YACED,MAAM;YACNJ,KAAK;gBAAE,GAAGK,aAAO,CAAC,kBAAkB;YAAC;QACvC;QAEA;YACED,MAAM;YACNJ,KAAK;gBAAE,GAAGK,aAAO,CAAC,iBAAiB;YAAC;QACtC;KACD;IACDC,WAAW;QACTC,SAASb,QAAQC,GAAG,CAACC,EAAE,KAAK,SAAS,eAAe;QACpDY,KAAK;QACLC,qBAAqBf,QAAQC,GAAG,CAACC,EAAE,KAAK;IAC1C;AACF", "ignoreList": [0]}