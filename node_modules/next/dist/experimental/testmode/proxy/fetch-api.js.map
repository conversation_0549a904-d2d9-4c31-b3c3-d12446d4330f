{"version": 3, "sources": ["../../../../src/experimental/testmode/proxy/fetch-api.ts"], "sourcesContent": ["import type { ProxyFetchRequest, ProxyResponse } from './types'\nimport { ABORT, CONTINUE, UNHANDLED } from './types'\n\nexport type FetchHandlerResult =\n  | Response\n  | 'abort'\n  | 'continue'\n  | null\n  | undefined\n\nexport type FetchHandler = (\n  testData: string,\n  request: Request\n) => FetchHandlerResult | Promise<FetchHandlerResult>\n\nfunction buildRequest(req: ProxyFetchRequest): Request {\n  const { request: proxyRequest } = req\n  const { url, headers, body, ...options } = proxyRequest\n  return new Request(url, {\n    ...options,\n    headers: new Headers(headers),\n    body: body ? Buffer.from(body, 'base64') : null,\n  })\n}\n\nasync function buildResponse(\n  response: FetchHandlerResult\n): Promise<ProxyResponse> {\n  if (!response) {\n    return UNHANDLED\n  }\n  if (response === 'abort') {\n    return ABORT\n  }\n  if (response === 'continue') {\n    return CONTINUE\n  }\n\n  const { status, headers, body } = response\n  return {\n    api: 'fetch',\n    response: {\n      status,\n      headers: Array.from(headers),\n      body: body\n        ? Buffer.from(await response.arrayBuffer()).toString('base64')\n        : null,\n    },\n  }\n}\n\nexport async function handleFetch(\n  req: ProxyFetchRequest,\n  onFetch: FetchHandler\n): Promise<ProxyResponse> {\n  const { testData } = req\n  const request = buildRequest(req)\n  const response = await onFetch(testData, request)\n  return buildResponse(response)\n}\n"], "names": ["handleFetch", "buildRequest", "req", "request", "proxyRequest", "url", "headers", "body", "options", "Request", "Headers", "<PERSON><PERSON><PERSON>", "from", "buildResponse", "response", "UNHANDLED", "ABORT", "CONTINUE", "status", "api", "Array", "arrayBuffer", "toString", "onFetch", "testData"], "mappings": ";;;;+BAmDsBA;;;eAAAA;;;uBAlDqB;AAc3C,SAASC,aAAaC,GAAsB;IAC1C,MAAM,EAAEC,SAASC,YAAY,EAAE,GAAGF;IAClC,MAAM,EAAEG,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAE,GAAGC,SAAS,GAAGJ;IAC3C,OAAO,IAAIK,QAAQJ,KAAK;QACtB,GAAGG,OAAO;QACVF,SAAS,IAAII,QAAQJ;QACrBC,MAAMA,OAAOI,OAAOC,IAAI,CAACL,MAAM,YAAY;IAC7C;AACF;AAEA,eAAeM,cACbC,QAA4B;IAE5B,IAAI,CAACA,UAAU;QACb,OAAOC,gBAAS;IAClB;IACA,IAAID,aAAa,SAAS;QACxB,OAAOE,YAAK;IACd;IACA,IAAIF,aAAa,YAAY;QAC3B,OAAOG,eAAQ;IACjB;IAEA,MAAM,EAAEC,MAAM,EAAEZ,OAAO,EAAEC,IAAI,EAAE,GAAGO;IAClC,OAAO;QACLK,KAAK;QACLL,UAAU;YACRI;YACAZ,SAASc,MAAMR,IAAI,CAACN;YACpBC,MAAMA,OACFI,OAAOC,IAAI,CAAC,MAAME,SAASO,WAAW,IAAIC,QAAQ,CAAC,YACnD;QACN;IACF;AACF;AAEO,eAAetB,YACpBE,GAAsB,EACtBqB,OAAqB;IAErB,MAAM,EAAEC,QAAQ,EAAE,GAAGtB;IACrB,MAAMC,UAAUF,aAAaC;IAC7B,MAAMY,WAAW,MAAMS,QAAQC,UAAUrB;IACzC,OAAOU,cAAcC;AACvB", "ignoreList": [0]}