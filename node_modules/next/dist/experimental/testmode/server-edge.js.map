{"version": 3, "sources": ["../../../src/experimental/testmode/server-edge.ts"], "sourcesContent": ["import { withRequest as withRequestContext } from './context'\nimport { interceptFetch, reader } from './fetch'\n\nexport function interceptTestApis(): () => void {\n  return interceptFetch(global.fetch)\n}\n\nexport function wrapRequestHandler<T, TRequest extends Request>(\n  handler: (req: TRequest, fn: () => T) => T\n): (req: TRequest, fn: () => T) => T {\n  return (req, fn) => withRequestContext(req, reader, () => handler(req, fn))\n}\n"], "names": ["interceptTestApis", "wrapRequestHandler", "interceptFetch", "global", "fetch", "handler", "req", "fn", "withRequestContext", "reader"], "mappings": ";;;;;;;;;;;;;;;IAGgBA,iBAAiB;eAAjBA;;IAIAC,kBAAkB;eAAlBA;;;yBAPkC;uBACX;AAEhC,SAASD;IACd,OAAOE,IAAAA,qBAAc,EAACC,OAAOC,KAAK;AACpC;AAEO,SAASH,mBACdI,OAA0C;IAE1C,OAAO,CAACC,KAAKC,KAAOC,IAAAA,oBAAkB,EAACF,KAAKG,aAAM,EAAE,IAAMJ,QAAQC,KAAKC;AACzE", "ignoreList": [0]}