{"version": 3, "sources": ["../../../src/export/routes/app-page.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'node:http'\nimport type { ExportRouteResult } from '../types'\nimport type { RenderOpts } from '../../server/app-render/types'\nimport type { NextParsedUrlQuery } from '../../server/request-meta'\nimport type { RouteMetadata } from './types'\n\nimport type {\n  MockedRequest,\n  MockedResponse,\n} from '../../server/lib/mock-request'\nimport { isDynamicUsageError } from '../helpers/is-dynamic-usage-error'\nimport {\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_META_SUFFIX,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n} from '../../lib/constants'\nimport { hasNextSupport } from '../../server/ci-info'\nimport { lazyRenderAppPage } from '../../server/route-modules/app-page/module.render'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { NodeNextRequest, NodeNextResponse } from '../../server/base-http/node'\nimport { NEXT_IS_PRERENDER_HEADER } from '../../client/components/app-router-headers'\nimport type { FetchMetrics } from '../../server/base-http'\nimport type { WorkStore } from '../../server/app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from '../../server/request/fallback-params'\nimport { AfterRunner } from '../../server/after/run-with-after'\nimport type { RequestLifecycleOpts } from '../../server/base-server'\nimport type { AppSharedContext } from '../../server/app-render/app-render'\nimport type { MultiFileWriter } from '../../lib/multi-file-writer'\nimport { stringifyResumeDataCache } from '../../server/resume-data-cache/resume-data-cache'\n\n/**\n * Renders & exports a page associated with the /app directory\n */\nexport async function exportAppPage(\n  req: MockedRequest,\n  res: MockedResponse,\n  page: string,\n  path: string,\n  pathname: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: FallbackRouteParams | null,\n  partialRenderOpts: Omit<RenderOpts, keyof RequestLifecycleOpts>,\n  htmlFilepath: string,\n  debugOutput: boolean,\n  isDynamicError: boolean,\n  fileWriter: MultiFileWriter,\n  sharedContext: AppSharedContext\n): Promise<ExportRouteResult> {\n  const afterRunner = new AfterRunner()\n\n  const renderOpts: RenderOpts = {\n    ...partialRenderOpts,\n    waitUntil: afterRunner.context.waitUntil,\n    onClose: afterRunner.context.onClose,\n    onAfterTaskError: afterRunner.context.onTaskError,\n  }\n\n  let isDefaultNotFound = false\n  // If the page is `/_not-found`, then we should update the page to be `/404`.\n  // UNDERSCORE_NOT_FOUND_ROUTE value used here, however we don't want to import it here as it causes constants to be inlined which we don't want here.\n  if (page === '/_not-found/page') {\n    isDefaultNotFound = true\n    pathname = '/404'\n  }\n\n  try {\n    const result = await lazyRenderAppPage(\n      new NodeNextRequest(req),\n      new NodeNextResponse(res),\n      pathname,\n      query,\n      fallbackRouteParams,\n      renderOpts,\n      undefined,\n      false,\n      sharedContext\n    )\n\n    const html = result.toUnchunkedString()\n\n    // TODO(after): if we abort a prerender because of an error in an after-callback\n    // we should probably communicate that better (and not log the error twice)\n    await afterRunner.executeAfter()\n\n    const { metadata } = result\n    const {\n      flightData,\n      cacheControl = { revalidate: false, expire: undefined },\n      postponed,\n      fetchTags,\n      fetchMetrics,\n      segmentData,\n      renderResumeDataCache,\n    } = metadata\n\n    // Ensure we don't postpone without having PPR enabled.\n    if (postponed && !renderOpts.experimental.isRoutePPREnabled) {\n      throw new Error('Invariant: page postponed without PPR being enabled')\n    }\n\n    if (cacheControl.revalidate === 0) {\n      if (isDynamicError) {\n        throw new Error(\n          `Page with dynamic = \"error\" encountered dynamic data method on ${path}.`\n        )\n      }\n      const { staticBailoutInfo = {} } = metadata\n\n      if (debugOutput && staticBailoutInfo?.description) {\n        logDynamicUsageWarning({\n          path,\n          description: staticBailoutInfo.description,\n          stack: staticBailoutInfo.stack,\n        })\n      }\n\n      return { cacheControl, fetchMetrics }\n    }\n\n    // If page data isn't available, it means that the page couldn't be rendered\n    // properly so long as we don't have unknown route params. When a route doesn't\n    // have unknown route params, there will not be any flight data.\n    if (\n      !flightData &&\n      (!fallbackRouteParams || fallbackRouteParams.size === 0)\n    ) {\n      throw new Error(`Invariant: failed to get page data for ${path}`)\n    }\n\n    if (flightData) {\n      // If PPR is enabled, we want to emit a prefetch rsc file for the page\n      // instead of the standard rsc. This is because the standard rsc will\n      // contain the dynamic data. We do this if any routes have PPR enabled so\n      // that the cache read/write is the same.\n      if (renderOpts.experimental.isRoutePPREnabled) {\n        // If PPR is enabled, we should emit the flight data as the prefetch\n        // payload.\n        // TODO: This will eventually be replaced by the per-segment prefetch\n        // output below.\n        fileWriter.append(\n          htmlFilepath.replace(/\\.html$/, RSC_PREFETCH_SUFFIX),\n          flightData\n        )\n      } else {\n        // Writing the RSC payload to a file if we don't have PPR enabled.\n        fileWriter.append(\n          htmlFilepath.replace(/\\.html$/, RSC_SUFFIX),\n          flightData\n        )\n      }\n    }\n\n    let segmentPaths\n    if (segmentData) {\n      // Emit the per-segment prefetch data. We emit them as separate files\n      // so that the cache handler has the option to treat each as a\n      // separate entry.\n      segmentPaths = []\n      const segmentsDir = htmlFilepath.replace(\n        /\\.html$/,\n        RSC_SEGMENTS_DIR_SUFFIX\n      )\n\n      for (const [segmentPath, buffer] of segmentData) {\n        segmentPaths.push(segmentPath)\n        const segmentDataFilePath =\n          segmentsDir + segmentPath + RSC_SEGMENT_SUFFIX\n        fileWriter.append(segmentDataFilePath, buffer)\n      }\n    }\n\n    const headers: OutgoingHttpHeaders = { ...metadata.headers }\n\n    // If we're writing the file to disk, we know it's a prerender.\n    headers[NEXT_IS_PRERENDER_HEADER] = '1'\n\n    if (fetchTags) {\n      headers[NEXT_CACHE_TAGS_HEADER] = fetchTags\n    }\n\n    // Writing static HTML to a file.\n    fileWriter.append(htmlFilepath, html)\n\n    const isParallelRoute = /\\/@\\w+/.test(page)\n    const isNonSuccessfulStatusCode = res.statusCode > 300\n\n    // When PPR is enabled, we don't always send 200 for routes that have been\n    // pregenerated, so we should grab the status code from the mocked\n    // response.\n    let status: number | undefined = renderOpts.experimental.isRoutePPREnabled\n      ? res.statusCode\n      : undefined\n\n    if (isDefaultNotFound) {\n      // Override the default /_not-found page status code to 404\n      status = 404\n    } else if (isNonSuccessfulStatusCode && !isParallelRoute) {\n      // If it's parallel route the status from mock response is 404\n      status = res.statusCode\n    }\n\n    // Writing the request metadata to a file.\n    const meta: RouteMetadata = {\n      status,\n      headers,\n      postponed,\n      segmentPaths,\n    }\n\n    fileWriter.append(\n      htmlFilepath.replace(/\\.html$/, NEXT_META_SUFFIX),\n      JSON.stringify(meta, null, 2)\n    )\n\n    return {\n      // Only include the metadata if the environment has next support.\n      metadata: hasNextSupport ? meta : undefined,\n      hasEmptyStaticShell: Boolean(postponed) && html === '',\n      hasPostponed: Boolean(postponed),\n      cacheControl,\n      fetchMetrics,\n      renderResumeDataCache: renderResumeDataCache\n        ? await stringifyResumeDataCache(renderResumeDataCache)\n        : undefined,\n    }\n  } catch (err) {\n    if (!isDynamicUsageError(err)) {\n      throw err\n    }\n\n    // We should fail rendering if a client side rendering bailout\n    // occurred at the page level.\n    if (isBailoutToCSRError(err)) {\n      throw err\n    }\n\n    let fetchMetrics: FetchMetrics | undefined\n\n    if (debugOutput) {\n      const store = (renderOpts as any).store as WorkStore\n      const { dynamicUsageDescription, dynamicUsageStack } = store\n      fetchMetrics = store.fetchMetrics\n\n      logDynamicUsageWarning({\n        path,\n        description: dynamicUsageDescription ?? '',\n        stack: dynamicUsageStack,\n      })\n    }\n\n    return { cacheControl: { revalidate: 0, expire: undefined }, fetchMetrics }\n  }\n}\n\nfunction logDynamicUsageWarning({\n  path,\n  description,\n  stack,\n}: {\n  path: string\n  description: string\n  stack?: string\n}) {\n  const errMessage = new Error(\n    `Static generation failed due to dynamic usage on ${path}, reason: ${description}`\n  )\n\n  if (stack) {\n    errMessage.stack = errMessage.message + stack.substring(stack.indexOf('\\n'))\n  }\n\n  console.warn(errMessage)\n}\n"], "names": ["exportAppPage", "req", "res", "page", "path", "pathname", "query", "fallbackRouteParams", "partialRenderOpts", "htmlFilepath", "debugOutput", "isDynamicError", "fileWriter", "sharedContext", "after<PERSON><PERSON>ner", "After<PERSON><PERSON>ner", "renderOpts", "waitUntil", "context", "onClose", "onAfterTaskError", "onTaskError", "isDefaultNotFound", "result", "lazyRenderAppPage", "NodeNextRequest", "NodeNextResponse", "undefined", "html", "toUnchunkedString", "executeAfter", "metadata", "flightData", "cacheControl", "revalidate", "expire", "postponed", "fetchTags", "fetchMetrics", "segmentData", "renderResumeDataCache", "experimental", "isRoutePPREnabled", "Error", "staticBailoutInfo", "description", "logDynamicUsageWarning", "stack", "size", "append", "replace", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "segmentPaths", "segmentsDir", "RSC_SEGMENTS_DIR_SUFFIX", "segmentPath", "buffer", "push", "segmentDataFilePath", "RSC_SEGMENT_SUFFIX", "headers", "NEXT_IS_PRERENDER_HEADER", "NEXT_CACHE_TAGS_HEADER", "isParallelRoute", "test", "isNonSuccessfulStatusCode", "statusCode", "status", "meta", "NEXT_META_SUFFIX", "JSON", "stringify", "hasNextSupport", "hasEmptyStaticShell", "Boolean", "hasPostponed", "stringifyResumeDataCache", "err", "isDynamicUsageError", "isBailoutToCSRError", "store", "dynamicUsageDescription", "dynamicUsageStack", "errMessage", "message", "substring", "indexOf", "console", "warn"], "mappings": ";;;;+BAoCs<PERSON>;;;eAAAA;;;qCA1Bc;2BAQ7B;wBACwB;8BACG;8BACE;sBACc;kCACT;8BAIb;iCAIa;AAKlC,eAAeA,cACpBC,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,QAAgB,EAChBC,KAAyB,EACzBC,mBAA+C,EAC/CC,iBAA+D,EAC/DC,YAAoB,EACpBC,WAAoB,EACpBC,cAAuB,EACvBC,UAA2B,EAC3BC,aAA+B;IAE/B,MAAMC,cAAc,IAAIC,yBAAW;IAEnC,MAAMC,aAAyB;QAC7B,GAAGR,iBAAiB;QACpBS,WAAWH,YAAYI,OAAO,CAACD,SAAS;QACxCE,SAASL,YAAYI,OAAO,CAACC,OAAO;QACpCC,kBAAkBN,YAAYI,OAAO,CAACG,WAAW;IACnD;IAEA,IAAIC,oBAAoB;IACxB,6EAA6E;IAC7E,qJAAqJ;IACrJ,IAAInB,SAAS,oBAAoB;QAC/BmB,oBAAoB;QACpBjB,WAAW;IACb;IAEA,IAAI;QACF,MAAMkB,SAAS,MAAMC,IAAAA,+BAAiB,EACpC,IAAIC,qBAAe,CAACxB,MACpB,IAAIyB,sBAAgB,CAACxB,MACrBG,UACAC,OACAC,qBACAS,YACAW,WACA,OACAd;QAGF,MAAMe,OAAOL,OAAOM,iBAAiB;QAErC,gFAAgF;QAChF,2EAA2E;QAC3E,MAAMf,YAAYgB,YAAY;QAE9B,MAAM,EAAEC,QAAQ,EAAE,GAAGR;QACrB,MAAM,EACJS,UAAU,EACVC,eAAe;YAAEC,YAAY;YAAOC,QAAQR;QAAU,CAAC,EACvDS,SAAS,EACTC,SAAS,EACTC,YAAY,EACZC,WAAW,EACXC,qBAAqB,EACtB,GAAGT;QAEJ,uDAAuD;QACvD,IAAIK,aAAa,CAACpB,WAAWyB,YAAY,CAACC,iBAAiB,EAAE;YAC3D,MAAM,qBAAgE,CAAhE,IAAIC,MAAM,wDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE;QAEA,IAAIV,aAAaC,UAAU,KAAK,GAAG;YACjC,IAAIvB,gBAAgB;gBAClB,MAAM,qBAEL,CAFK,IAAIgC,MACR,CAAC,+DAA+D,EAAEvC,KAAK,CAAC,CAAC,GADrE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,EAAEwC,oBAAoB,CAAC,CAAC,EAAE,GAAGb;YAEnC,IAAIrB,gBAAekC,qCAAAA,kBAAmBC,WAAW,GAAE;gBACjDC,uBAAuB;oBACrB1C;oBACAyC,aAAaD,kBAAkBC,WAAW;oBAC1CE,OAAOH,kBAAkBG,KAAK;gBAChC;YACF;YAEA,OAAO;gBAAEd;gBAAcK;YAAa;QACtC;QAEA,4EAA4E;QAC5E,+EAA+E;QAC/E,gEAAgE;QAChE,IACE,CAACN,cACA,CAAA,CAACzB,uBAAuBA,oBAAoByC,IAAI,KAAK,CAAA,GACtD;YACA,MAAM,qBAA2D,CAA3D,IAAIL,MAAM,CAAC,uCAAuC,EAAEvC,MAAM,GAA1D,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QAEA,IAAI4B,YAAY;YACd,sEAAsE;YACtE,qEAAqE;YACrE,yEAAyE;YACzE,yCAAyC;YACzC,IAAIhB,WAAWyB,YAAY,CAACC,iBAAiB,EAAE;gBAC7C,oEAAoE;gBACpE,WAAW;gBACX,qEAAqE;gBACrE,gBAAgB;gBAChB9B,WAAWqC,MAAM,CACfxC,aAAayC,OAAO,CAAC,WAAWC,8BAAmB,GACnDnB;YAEJ,OAAO;gBACL,kEAAkE;gBAClEpB,WAAWqC,MAAM,CACfxC,aAAayC,OAAO,CAAC,WAAWE,qBAAU,GAC1CpB;YAEJ;QACF;QAEA,IAAIqB;QACJ,IAAId,aAAa;YACf,qEAAqE;YACrE,8DAA8D;YAC9D,kBAAkB;YAClBc,eAAe,EAAE;YACjB,MAAMC,cAAc7C,aAAayC,OAAO,CACtC,WACAK,kCAAuB;YAGzB,KAAK,MAAM,CAACC,aAAaC,OAAO,IAAIlB,YAAa;gBAC/Cc,aAAaK,IAAI,CAACF;gBAClB,MAAMG,sBACJL,cAAcE,cAAcI,6BAAkB;gBAChDhD,WAAWqC,MAAM,CAACU,qBAAqBF;YACzC;QACF;QAEA,MAAMI,UAA+B;YAAE,GAAG9B,SAAS8B,OAAO;QAAC;QAE3D,+DAA+D;QAC/DA,OAAO,CAACC,0CAAwB,CAAC,GAAG;QAEpC,IAAIzB,WAAW;YACbwB,OAAO,CAACE,iCAAsB,CAAC,GAAG1B;QACpC;QAEA,iCAAiC;QACjCzB,WAAWqC,MAAM,CAACxC,cAAcmB;QAEhC,MAAMoC,kBAAkB,SAASC,IAAI,CAAC9D;QACtC,MAAM+D,4BAA4BhE,IAAIiE,UAAU,GAAG;QAEnD,0EAA0E;QAC1E,kEAAkE;QAClE,YAAY;QACZ,IAAIC,SAA6BpD,WAAWyB,YAAY,CAACC,iBAAiB,GACtExC,IAAIiE,UAAU,GACdxC;QAEJ,IAAIL,mBAAmB;YACrB,2DAA2D;YAC3D8C,SAAS;QACX,OAAO,IAAIF,6BAA6B,CAACF,iBAAiB;YACxD,8DAA8D;YAC9DI,SAASlE,IAAIiE,UAAU;QACzB;QAEA,0CAA0C;QAC1C,MAAME,OAAsB;YAC1BD;YACAP;YACAzB;YACAiB;QACF;QAEAzC,WAAWqC,MAAM,CACfxC,aAAayC,OAAO,CAAC,WAAWoB,2BAAgB,GAChDC,KAAKC,SAAS,CAACH,MAAM,MAAM;QAG7B,OAAO;YACL,iEAAiE;YACjEtC,UAAU0C,sBAAc,GAAGJ,OAAO1C;YAClC+C,qBAAqBC,QAAQvC,cAAcR,SAAS;YACpDgD,cAAcD,QAAQvC;YACtBH;YACAK;YACAE,uBAAuBA,wBACnB,MAAMqC,IAAAA,yCAAwB,EAACrC,yBAC/Bb;QACN;IACF,EAAE,OAAOmD,KAAK;QACZ,IAAI,CAACC,IAAAA,wCAAmB,EAACD,MAAM;YAC7B,MAAMA;QACR;QAEA,8DAA8D;QAC9D,8BAA8B;QAC9B,IAAIE,IAAAA,iCAAmB,EAACF,MAAM;YAC5B,MAAMA;QACR;QAEA,IAAIxC;QAEJ,IAAI5B,aAAa;YACf,MAAMuE,QAAQ,AAACjE,WAAmBiE,KAAK;YACvC,MAAM,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAE,GAAGF;YACvD3C,eAAe2C,MAAM3C,YAAY;YAEjCQ,uBAAuB;gBACrB1C;gBACAyC,aAAaqC,2BAA2B;gBACxCnC,OAAOoC;YACT;QACF;QAEA,OAAO;YAAElD,cAAc;gBAAEC,YAAY;gBAAGC,QAAQR;YAAU;YAAGW;QAAa;IAC5E;AACF;AAEA,SAASQ,uBAAuB,EAC9B1C,IAAI,EACJyC,WAAW,EACXE,KAAK,EAKN;IACC,MAAMqC,aAAa,qBAElB,CAFkB,IAAIzC,MACrB,CAAC,iDAAiD,EAAEvC,KAAK,UAAU,EAAEyC,aAAa,GADjE,qBAAA;eAAA;oBAAA;sBAAA;IAEnB;IAEA,IAAIE,OAAO;QACTqC,WAAWrC,KAAK,GAAGqC,WAAWC,OAAO,GAAGtC,MAAMuC,SAAS,CAACvC,MAAMwC,OAAO,CAAC;IACxE;IAEAC,QAAQC,IAAI,CAACL;AACf", "ignoreList": [0]}