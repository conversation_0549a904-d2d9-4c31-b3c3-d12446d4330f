# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.3.0](https://github.com/ljharb/es-to-primitive/compare/v1.2.1...v1.3.0) - 2024-11-26

### Commits

- [actions] reuse common workflows [`bb72efc`](https://github.com/ljharb/es-to-primitive/commit/bb72efc7e04ae11b84e4aecf120a4e9063e34428)
- [Tests] use `es-value-fixtures` [`a912f7b`](https://github.com/ljharb/es-to-primitive/commit/a912f7b675333735c1c980cda88772ac1870395b)
- [Tests] migrate tests to Github Actions [`510baf0`](https://github.com/ljharb/es-to-primitive/commit/510baf092633a62d59866fbf56836ce42c717c70)
- [New] add types [`69ba1fd`](https://github.com/ljharb/es-to-primitive/commit/69ba1fdcac834b03698739990ba98fe6007024dc)
- [meta] remove unused Makefile [`4ea66e6`](https://github.com/ljharb/es-to-primitive/commit/4ea66e62ef4afa0102eb8335ba3e003e8332f664)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`3c31937`](https://github.com/ljharb/es-to-primitive/commit/3c31937119ca24fd6d00e362d6435a28cfe9e91c)
- [meta] do not publish github action workflow files [`389567e`](https://github.com/ljharb/es-to-primitive/commit/389567e8523b65b90b529f1029d215fd4f12ac14)
- [meta] use `npmignore` to autogenerate an npmignore file [`9f3aa76`](https://github.com/ljharb/es-to-primitive/commit/9f3aa7651791ab9386408035491a1ba4fec4c432)
- [actions] split out node 10-20, and 20+ [`c60d7d8`](https://github.com/ljharb/es-to-primitive/commit/c60d7d822a36880bce0535335c70fdc2a8da232d)
- [Tests] run `nyc` on all tests; use `tape` runner [`29cbb89`](https://github.com/ljharb/es-to-primitive/commit/29cbb89800b5cfef9bef3ae7e0e779c782e1bbb9)
- [meta] add `auto-changelog` [`ea744b2`](https://github.com/ljharb/es-to-primitive/commit/ea744b2a0bda788b0d957c1787e41434e98b0155)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `function.prototype.name`, `has-symbols`, `object-inspect`, `object-is`, `tape` [`e5c3c79`](https://github.com/ljharb/es-to-primitive/commit/e5c3c792f67685a9647e817e7582d4c76a876f69)
- [actions] add automatic rebasing / merge commit blocking [`a5a6f00`](https://github.com/ljharb/es-to-primitive/commit/a5a6f0066540c91c8aa45a4921f1cd2349f435ba)
- [Dev Deps] update `@ljharb/eslint-config`, `es-value-fixtures`, `function.prototype.name`, `npmignore`, `object-inspect`, `object-is`, `tape` [`7941fd5`](https://github.com/ljharb/es-to-primitive/commit/7941fd530fb3a73f923b76c739335ffc21793ad6)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `es-value-fixtures`, `foreach`, `object-inspect`, `tape` [`eb1c79c`](https://github.com/ljharb/es-to-primitive/commit/eb1c79c288c89154014634b94f64308344901eaf)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `function.prototype.name`, `object-inspect`, `safe-publish-latest`, `tape` [`249b42f`](https://github.com/ljharb/es-to-primitive/commit/249b42f1ce069ea78a032f10414d1c1c0b6c6345)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `function.prototype.name`, `object-inspect`, `object-is`, `tape` [`d57d5e9`](https://github.com/ljharb/es-to-primitive/commit/d57d5e9ea5ea4778f383e2f1aa637be0be80dd78)
- [actions] update codecov uploader [`003b62c`](https://github.com/ljharb/es-to-primitive/commit/003b62c483372d5eac38f51925b6cbdf5d7a0665)
- [actions] add "Allow Edits" workflow [`75ee990`](https://github.com/ljharb/es-to-primitive/commit/75ee99083626dc14558ae294c127e4aaf925d214)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`, `object-is`; add `safe-publish-latest` [`ba5da7b`](https://github.com/ljharb/es-to-primitive/commit/ba5da7bffd93c3cc2e079ad751a3e678333a973e)
- [readme] remove travis badge [`6f7aec7`](https://github.com/ljharb/es-to-primitive/commit/6f7aec78e4f1ebfca74c384a837063f4099e7b9b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`3291fd5`](https://github.com/ljharb/es-to-primitive/commit/3291fd567695b45bddc58e5ec3da2dcce0e5ccc7)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `function.prototype.name`, `has-symbols`, `object-inspect` [`53007f2`](https://github.com/ljharb/es-to-primitive/commit/53007f25d1f26e301b4f41d070c423723bed1690)
- [actions] update checkout action [`69640db`](https://github.com/ljharb/es-to-primitive/commit/69640dbb9ddafe05527388fe72bda1aca08d07b5)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-is`, `tape`; add `aud` [`c9d644e`](https://github.com/ljharb/es-to-primitive/commit/c9d644ef3c6b2210e86ce2d3aa8e8b1668f6801d)
- [Tests] use `for-each` instead of `foreach` [`e9117bb`](https://github.com/ljharb/es-to-primitive/commit/e9117bb055417cb721dbf5dbe1d23b058a8241f2)
- [readme] add github actions/codecov badges [`53cd375`](https://github.com/ljharb/es-to-primitive/commit/53cd375ab22a25d4bada35000473e30c22ee2028)
- [Deps] update `is-callable`, `is-date-object`, `is-symbol` [`8116c68`](https://github.com/ljharb/es-to-primitive/commit/8116c68a8ba555f8daaf1d71a60c974d3439c94b)
- [Tests] fix test skipping for `Symbol.toPrimitive` [`e6268ef`](https://github.com/ljharb/es-to-primitive/commit/e6268ef31b34cb5263501ba9735ccce78a07e504)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`da41c40`](https://github.com/ljharb/es-to-primitive/commit/da41c40399c2a574f74a59b85800d9934b91d49a)
- [Deps] update `is-callable`, `is-date-object` [`96fe13f`](https://github.com/ljharb/es-to-primitive/commit/96fe13ff3c486c7857c2ca69ac70161ef0e5b4a1)
- [Tests] replace `aud` with `npm audit` [`0b53154`](https://github.com/ljharb/es-to-primitive/commit/0b531546081427cb8a4fc06fde5540ba0b287b5b)
- [meta] use `prepublishOnly` script for npm 7+ [`9d7d485`](https://github.com/ljharb/es-to-primitive/commit/9d7d4856d4b5f28c68de2aba068522b9a85ee669)
- [Deps] update `is-callable` [`3c990b6`](https://github.com/ljharb/es-to-primitive/commit/3c990b646813e2470b19460e32801113f9acc13b)
- [Deps] update `is-callable` [`9bcfff2`](https://github.com/ljharb/es-to-primitive/commit/9bcfff276ce078034404b6b27e4f74beb530002c)
- [Deps] update `is-callable` [`1eb5478`](https://github.com/ljharb/es-to-primitive/commit/1eb5478e0c93b230b7bc67f9fef963d94a391117)
- [meta] only run `aud` on prod deps [`1fcd896`](https://github.com/ljharb/es-to-primitive/commit/1fcd89684a4351c15fec2cb289ecc331f917b80e)
- [Deps] update `is-symbol` [`7174a47`](https://github.com/ljharb/es-to-primitive/commit/7174a474f4f9f07319c81f046b10446caf9b3af0)

<!-- auto-changelog-above -->

1.2.1 / 2019-11-08
=================
  * [readme] remove testling URLs
  * [meta] add `funding` field
  * [meta] create FUNDING.yml
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `replace`, `semver`, `tape`, `function.prototype.name`
  * [Tests] use shared travis-ci configs
  * [Tests] Add es5 tests for `symbol` types (#45)
  * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops
  * [Tests] remove `jscs`

1.2.0 / 2018-09-27
=================
  * [New] create ES2015 entry point/property, to replace ES6
  * [Fix] Ensure optional arguments are not part of the length (#29)
  * [Deps] update `is-callable`
  * [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `semver`, `object-inspect`, `replace`
  * [Tests] avoid util.inspect bug with `new Date(NaN)` on node v6.0 and v6.1.
  * [Tests] up to `node` `v10.11`, `v9.11`, `v8.12`, `v6.14`, `v4.9`

1.1.1 / 2016-01-03
=================
  * [Fix: ES5] fix coercion logic: ES5’s ToPrimitive does not coerce any primitive value, regardless of hint (#2)

1.1.0 / 2015-12-27
=================
  * [New] add `Symbol.toPrimitive` support
  * [Deps] update `is-callable`, `is-date-object`
  * [Dev Deps] update `eslint`, `tape`, `semver`, `jscs`, `covert`, `nsp`, `@ljharb/eslint-config`
  * [Dev Deps] remove unused deps
  * [Tests] up to `node` `v5.3`
  * [Tests] fix npm upgrades on older node versions
  * [Tests] fix testling
  * [Docs] Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG

1.0.1 / 2016-01-03
=================
  * [Fix: ES5] fix coercion logic: ES5’s ToPrimitive does not coerce any primitive value, regardless of hint (#2)
  * [Deps] update `is-callable`, `is-date-object`
  * [Dev Deps] update `eslint`, `tape`, `semver`, `jscs`, `covert`, `nsp`, `@ljharb/eslint-config`
  * [Dev Deps] remove unused deps
  * [Tests] up to `node` `v5.3`
  * [Tests] fix npm upgrades on older node versions
  * [Tests] fix testling
  * [Docs] Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG

1.0.0 / 2015-03-19
=================
  * Initial release.
