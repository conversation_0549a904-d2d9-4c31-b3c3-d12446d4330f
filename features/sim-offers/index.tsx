import { OffersListScreen } from './screens/OffersListScreen'
import { OfferDetailsScreen } from './screens/OfferDetailsScreen'
import { ContactScreen } from './screens/ContactScreen'

export const SimOffersRouter = ({ path }: { path: string[] }) => {
  const [route, id] = path
  
  switch (route) {
    case undefined:
      return <OffersListScreen />
    case 'offers':
      return <OfferDetailsScreen id={id} />
    case 'contact':
      return <ContactScreen />
    default:
      return <OffersListScreen />
  }
}

// Export components for external use
export { OffersListScreen, OfferDetailsScreen, ContactScreen }