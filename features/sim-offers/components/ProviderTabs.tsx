import { Provider } from '@/types'

interface ProviderTabsProps {
  providers: Provider[]
  selected: string
  onSelect: (providerId: string) => void
}

export function ProviderTabs({ providers, selected, onSelect }: ProviderTabsProps) {
  return (
    <div className="px-4 py-3 border-b border-gray-700">
      <div className="flex gap-2 overflow-x-auto scrollbar-hide">
        {providers.map(provider => (
          <button
            key={provider.id}
            onClick={() => onSelect(provider.id)}
            className={`provider-tab whitespace-nowrap ${
              selected === provider.id ? 'active' : 'inactive'
            }`}
            style={{
              backgroundColor: selected === provider.id ? provider.color : undefined
            }}
          >
            {provider.name_bn}
          </button>
        ))}
      </div>
    </div>
  )
}