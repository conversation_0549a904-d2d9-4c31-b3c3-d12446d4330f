import Link from 'next/link'
import { Offer } from '@/types'
import { PROVIDERS } from '@/data/offers'

interface OfferCardProps {
  offer: Offer
}

export function OfferCard({ offer }: OfferCardProps) {
  const provider = PROVIDERS.find(p => p.id === offer.provider)
  
  return (
    <Link href={`/sim-offers/offers/${offer.id}`}>
      <div 
        className="offer-card hover:bg-gray-750"
        style={{ borderLeftColor: provider?.color }}
      >
        {/* Header */}
        <div className="flex justify-between items-start mb-2">
          <div>
            <h3 className="font-semibold text-lg">{offer.title_bn}</h3>
            <p className="text-sm text-gray-400">{offer.title_en}</p>
          </div>
          {offer.special_note && (
            <span className="bg-yellow-500 text-black px-2 py-1 rounded text-xs font-bold">
              {offer.special_note}
            </span>
          )}
        </div>

        {/* Price Section */}
        <div className="flex justify-between items-center mb-3">
          <div>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-green-400">৳{offer.my_price}</span>
              <span className="text-sm text-gray-400 line-through">৳{offer.company_price}</span>
            </div>
            <div className="savings-badge">৳{offer.savings} সাশ্রয়</div>
          </div>
          
          <div className="text-right">
            <div className="text-sm text-gray-300">{offer.validity_days} দিন</div>
            {offer.minutes && (
              <div className="text-xs text-gray-400">{offer.minutes} মিনিট</div>
            )}
          </div>
        </div>

        {/* Provider Badge */}
        <div className="flex justify-between items-center">
          <span 
            className="px-3 py-1 rounded-full text-xs font-medium text-white"
            style={{ backgroundColor: provider?.color }}
          >
            {provider?.name_bn}
          </span>
          
          {!offer.is_available && (
            <span className="text-red-400 text-xs">স্টক নেই</span>
          )}
        </div>
      </div>
    </Link>
  )
}