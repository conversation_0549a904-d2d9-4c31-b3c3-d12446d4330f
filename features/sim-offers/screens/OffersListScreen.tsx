"use client"

import { useState } from 'react'
import { SAMPLE_OFFERS, PROVIDERS } from '@/data/offers'
import { OfferCard } from '../components/OfferCard'
import { ProviderTabs } from '../components/ProviderTabs'
import { SearchBar } from '../components/SearchBar'

export function OffersListScreen() {
  const [selectedProvider, setSelectedProvider] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')

  const filteredOffers = SAMPLE_OFFERS.filter(offer => {
    const matchesProvider = selectedProvider === 'all' || offer.provider === selectedProvider
    const matchesSearch = offer.title_bn.includes(searchTerm) || 
                         offer.title_en.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesProvider && matchesSearch
  })

  const featuredOffers = filteredOffers.filter(offer => offer.is_featured)

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="sticky top-0 bg-gray-900 z-10 p-4 border-b border-gray-700">
        <h1 className="text-xl font-bold text-center mb-4">রিয়াদের সিম অফার</h1>
        <SearchBar value={searchTerm} onChange={setSearchTerm} />
      </div>

      {/* Provider Tabs */}
      <ProviderTabs 
        providers={PROVIDERS}
        selected={selectedProvider}
        onSelect={setSelectedProvider}
      />

      {/* Content */}
      <div className="p-4">
        {/* Featured Offers */}
        {featuredOffers.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-3 text-yellow-400">⭐ ফিচার্ড অফার</h2>
            <div className="space-y-3">
              {featuredOffers.map(offer => (
                <OfferCard key={offer.id} offer={offer} />
              ))}
            </div>
          </div>
        )}

        {/* All Offers */}
        <div>
          <h2 className="text-lg font-semibold mb-3">সব অফার</h2>
          <div className="space-y-3">
            {filteredOffers.map(offer => (
              <OfferCard key={offer.id} offer={offer} />
            ))}
          </div>
        </div>

        {filteredOffers.length === 0 && (
          <div className="text-center py-8 text-gray-400">
            কোন অফার পাওয়া যায়নি
          </div>
        )}
      </div>
    </div>
  )
}