import { Offer, Provider, ContactInfo } from '@/types'

export const PROVIDERS: Provider[] = [
  { id: 'all', name_bn: 'সব', name_en: 'All', color: '#6B7280' },
  { id: 'grameenphone', name_bn: 'গ্রামীণফোন', name_en: 'Grameenphone', color: '#00A651' },
  { id: 'airtel', name_bn: 'এয়ারটেল', name_en: 'Airtel', color: '#E31E24' },
  { id: 'robi', name_bn: 'রবি', name_en: 'Robi', color: '#FF6B00' },
  { id: 'banglalink', name_bn: 'বাংলালিংক', name_en: 'Banglalink', color: '#0066CC' }
]

export const SAMPLE_OFFERS: Offer[] = [
  {
    id: '1',
    provider: 'grameenphone',
    title_bn: '৫০জিবি ৩০দিন',
    title_en: '50GB 30Days',
    data_amount: '50',
    data_unit: 'GB',
    my_price: 450,
    company_price: 500,
    savings: 50,
    validity_days: 30,
    offer_type: 'data',
    is_featured: true,
    is_available: true,
    special_note: 'সুপার ডিল'
  },
  {
    id: '2',
    provider: 'airtel',
    title_bn: '২০জিবি+৫০০মিনিট',
    title_en: '20GB+500Min',
    data_amount: '20',
    data_unit: 'GB',
    minutes: 500,
    my_price: 380,
    company_price: 450,
    savings: 70,
    validity_days: 30,
    offer_type: 'combo',
    is_featured: true,
    is_available: true
  },
  {
    id: '3',
    provider: 'robi',
    title_bn: '১৫জিবি ৭দিন',
    title_en: '15GB 7Days',
    data_amount: '15',
    data_unit: 'GB',
    my_price: 180,
    company_price: 220,
    savings: 40,
    validity_days: 7,
    offer_type: 'data',
    is_featured: false,
    is_available: true,
    special_note: 'দ্রুত শেষ'
  }
]

export const CONTACT_INFO: ContactInfo = {
  phone: '***********',
  whatsapp: '***********',
  telegram: '@Riyad99522',
  business_hours: '৯ সকাল - ১০ রাত'
}