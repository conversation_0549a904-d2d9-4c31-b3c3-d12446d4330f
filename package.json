{"name": "modern-nextjs-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate"}, "dependencies": {"next": "^15.0.0", "react": "^18.3.0", "react-dom": "^18.3.0", "@prisma/client": "^5.7.0", "@tanstack/react-query": "^5.0.0", "zustand": "^4.4.0", "react-hook-form": "^7.48.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "@radix-ui/react-slot": "^1.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.300.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.0.0", "postcss": "^8.0.0", "tailwindcss": "^3.3.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "prisma": "^5.7.0"}}